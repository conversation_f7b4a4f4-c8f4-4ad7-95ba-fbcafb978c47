{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":97,"byte_end":111,"line_start":4,"line_end":4,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":93,"byte_end":113,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use whatsmeow_types::Jid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `recipient`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":506,"byte_end":515,"line_start":22,"line_end":22,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        recipient: &Jid,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":506,"byte_end":515,"line_start":22,"line_end":22,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        recipient: &Jid,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_recipient","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `recipient`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:22:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        recipient: &Jid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_recipient`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `plaintext`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":531,"byte_end":540,"line_start":23,"line_end":23,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        plaintext: &[u8],","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":531,"byte_end":540,"line_start":23,"line_end":23,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        plaintext: &[u8],","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_plaintext","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `plaintext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:23:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        plaintext: &[u8],\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_plaintext`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sender`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":821,"byte_end":827,"line_start":32,"line_end":32,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        sender: &Jid,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":821,"byte_end":827,"line_start":32,"line_end":32,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        sender: &Jid,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_sender","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `sender`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:32:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sender: &Jid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_sender`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ciphertext`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":843,"byte_end":853,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        ciphertext: &[u8],","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":843,"byte_end":853,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        ciphertext: &[u8],","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_ciphertext","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `ciphertext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:33:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ciphertext: &[u8],\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_ciphertext`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `group_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1132,"byte_end":1140,"line_start":42,"line_end":42,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        group_id: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1132,"byte_end":1140,"line_start":42,"line_end":42,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        group_id: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_group_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `group_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:42:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        group_id: &str,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_group_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `plaintext`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1156,"byte_end":1165,"line_start":43,"line_end":43,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        plaintext: &[u8],","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1156,"byte_end":1165,"line_start":43,"line_end":43,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        plaintext: &[u8],","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_plaintext","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `plaintext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:43:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        plaintext: &[u8],\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_plaintext`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `group_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1482,"byte_end":1490,"line_start":54,"line_end":54,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        group_id: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1482,"byte_end":1490,"line_start":54,"line_end":54,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        group_id: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_group_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `group_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:54:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        group_id: &str,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_group_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sender`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1506,"byte_end":1512,"line_start":55,"line_end":55,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        sender: &Jid,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1506,"byte_end":1512,"line_start":55,"line_end":55,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        sender: &Jid,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_sender","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `sender`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:55:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sender: &Jid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_sender`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ciphertext`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1528,"byte_end":1538,"line_start":56,"line_end":56,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        ciphertext: &[u8],","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":1528,"byte_end":1538,"line_start":56,"line_end":56,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        ciphertext: &[u8],","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_ciphertext","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `ciphertext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:56:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ciphertext: &[u8],\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_ciphertext`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\keys.rs","byte_start":6312,"byte_end":6313,"line_start":176,"line_end":176,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"            .map_err(|e| CryptoError::SignatureVerificationFailed)?;","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\keys.rs","byte_start":6312,"byte_end":6313,"line_start":176,"line_end":176,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"            .map_err(|e| CryptoError::SignatureVerificationFailed)?;","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\keys.rs:176:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .map_err(|e| CryptoError::SignatureVerificationFailed)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `server_hello`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\noise.rs","byte_start":1469,"byte_end":1481,"line_start":54,"line_end":54,"column_start":44,"column_end":56,"is_primary":true,"text":[{"text":"    pub fn process_server_hello(&mut self, server_hello: &[u8]) -> Result<NoiseKeys, CryptoError> {","highlight_start":44,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\noise.rs","byte_start":1469,"byte_end":1481,"line_start":54,"line_end":54,"column_start":44,"column_end":56,"is_primary":true,"text":[{"text":"    pub fn process_server_hello(&mut self, server_hello: &[u8]) -> Result<NoiseKeys, CryptoError> {","highlight_start":44,"highlight_end":56}],"label":null,"suggested_replacement":"_server_hello","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `server_hello`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\noise.rs:54:44\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn process_server_hello(&mut self, server_hello: &[u8]) -> Result<NoiseKeys, CryptoError> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_server_hello`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `ephemeral_key` and `static_key` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\noise.rs","byte_start":173,"byte_end":187,"line_start":7,"line_end":7,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct NoiseHandshake {","highlight_start":12,"highlight_end":26}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-crypto\\src\\noise.rs","byte_start":194,"byte_end":207,"line_start":8,"line_end":8,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    ephemeral_key: KeyPair,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-crypto\\src\\noise.rs","byte_start":222,"byte_end":232,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    static_key: KeyPair,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `ephemeral_key` and `static_key` are never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\noise.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct NoiseHandshake {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ephemeral_key: KeyPair,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    static_key: KeyPair,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"you should consider adding a `Default` implementation for `SignalProtocol`","code":{"code":"clippy::new_without_default","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":298,"byte_end":405,"line_start":13,"line_end":17,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":5,"highlight_end":27},{"text":"        Self {","highlight_start":1,"highlight_end":15},{"text":"            // TODO: Initialize with store references","highlight_start":1,"highlight_end":54},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::new_without_default)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try adding this","code":null,"level":"help","spans":[{"file_name":"whatsmeow-crypto\\src\\signal.rs","byte_start":272,"byte_end":272,"line_start":12,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"impl SignalProtocol {","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"impl Default for SignalProtocol {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you should consider adding a `Default` implementation for `SignalProtocol`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-crypto\\src\\signal.rs:13:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Self {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|_____^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::new_without_default)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try adding this\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ impl Default for SignalProtocol {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+     fn default() -> Self {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+         Self::new()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+     }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"14 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 14 warnings emitted\u001b[0m\n\n"}
