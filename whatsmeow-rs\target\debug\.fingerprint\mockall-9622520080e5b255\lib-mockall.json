{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"nightly\"]", "target": 16297729212658632601, "profile": 15657897354478470176, "path": 11062113603515500590, "deps": [[2828590642173593838, "cfg_if", false, 9843122439619667624], [3016941897346161952, "downcast", false, 14069955155097941009], [7886665781035375288, "fragile", false, 14398319293988586803], [9001202093189684693, "mockall_derive", false, 12967573252433683369], [12516616738327129663, "predicates_tree", false, 7716920511731092958], [15863765456528386755, "predicates", false, 12560726616147315847]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\mockall-9622520080e5b255\\dep-lib-mockall", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}