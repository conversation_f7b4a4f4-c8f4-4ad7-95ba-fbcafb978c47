D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libgo_vector_validation-8959fbc68fcd4acb.rmeta: whatsmeow-binary\tests\go_vector_validation.rs whatsmeow-binary\tests\test_vectors.json Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\go_vector_validation-8959fbc68fcd4acb.d: whatsmeow-binary\tests\go_vector_validation.rs whatsmeow-binary\tests\test_vectors.json Cargo.toml

whatsmeow-binary\tests\go_vector_validation.rs:
whatsmeow-binary\tests\test_vectors.json:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
