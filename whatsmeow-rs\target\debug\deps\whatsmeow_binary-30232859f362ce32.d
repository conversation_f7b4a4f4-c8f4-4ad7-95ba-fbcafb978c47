D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libwhatsmeow_binary-30232859f362ce32.rmeta: whatsmeow-binary\src\lib.rs whatsmeow-binary\src\error.rs whatsmeow-binary\src\node.rs whatsmeow-binary\src\parser.rs whatsmeow-binary\src\serializer.rs whatsmeow-binary\src\token.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\whatsmeow_binary-30232859f362ce32.d: whatsmeow-binary\src\lib.rs whatsmeow-binary\src\error.rs whatsmeow-binary\src\node.rs whatsmeow-binary\src\parser.rs whatsmeow-binary\src\serializer.rs whatsmeow-binary\src\token.rs Cargo.toml

whatsmeow-binary\src\lib.rs:
whatsmeow-binary\src\error.rs:
whatsmeow-binary\src\node.rs:
whatsmeow-binary\src\parser.rs:
whatsmeow-binary\src\serializer.rs:
whatsmeow-binary\src\token.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
