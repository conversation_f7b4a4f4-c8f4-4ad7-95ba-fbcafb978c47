D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libwhatsmeow_rs-d64538ffa288bd97.rmeta: whatsmeow-rs\src\lib.rs whatsmeow-rs\src\appstate.rs whatsmeow-rs\src\client.rs whatsmeow-rs\src\error.rs whatsmeow-rs\src\events.rs whatsmeow-rs\src\group.rs whatsmeow-rs\src\media.rs whatsmeow-rs\src\message.rs whatsmeow-rs\src\presence.rs whatsmeow-rs\src\receipt.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\whatsmeow_rs-d64538ffa288bd97.d: whatsmeow-rs\src\lib.rs whatsmeow-rs\src\appstate.rs whatsmeow-rs\src\client.rs whatsmeow-rs\src\error.rs whatsmeow-rs\src\events.rs whatsmeow-rs\src\group.rs whatsmeow-rs\src\media.rs whatsmeow-rs\src\message.rs whatsmeow-rs\src\presence.rs whatsmeow-rs\src\receipt.rs Cargo.toml

whatsmeow-rs\src\lib.rs:
whatsmeow-rs\src\appstate.rs:
whatsmeow-rs\src\client.rs:
whatsmeow-rs\src\error.rs:
whatsmeow-rs\src\events.rs:
whatsmeow-rs\src\group.rs:
whatsmeow-rs\src\media.rs:
whatsmeow-rs\src\message.rs:
whatsmeow-rs\src\presence.rs:
whatsmeow-rs\src\receipt.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
