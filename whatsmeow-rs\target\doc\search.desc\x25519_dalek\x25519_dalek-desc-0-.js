searchState.loadedDescShard("x25519_dalek", 0, "x25519-dalek    CI\nA short-lived Di<PERSON>ie-<PERSON>man secret key that can only be …\nA <PERSON><PERSON><PERSON>-<PERSON> public key\nThe result of a <PERSON><PERSON><PERSON><PERSON><PERSON> key exchange.\nThe X25519 basepoint, for use with the bare, byte-oriented …\nView this public key as a byte array.\nView this shared secret key as a byte array.\nView this public key as a byte array.\nView this shared secret key as a byte array.\nPerform a Di<PERSON>ie-<PERSON>man key agreement between <code>self</code> and …\nReturns the argument unchanged.\nGiven an x25519 <code>EphemeralSecret</code> key, compute its …\nGiven a byte array, construct a x25519 <code>PublicKey</code>.\nReturns the argument unchanged.\nReturns the argument unchanged.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nGenerate a new <code>EphemeralSecret</code> with the supplied RNG.\nGenerate a new <code>EphemeralSecret</code> with the supplied RNG.\nConvert this public key to a byte array.\nConvert this shared secret to a byte array.\nEnsure in constant-time that this shared secret did not …\nThe bare, byte-oriented x25519 function, exactly as …")