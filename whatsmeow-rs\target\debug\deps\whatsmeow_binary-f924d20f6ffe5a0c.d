D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libwhatsmeow_binary-f924d20f6ffe5a0c.rmeta: whatsmeow-binary\src\lib.rs whatsmeow-binary\src\error.rs whatsmeow-binary\src\node.rs whatsmeow-binary\src\parser.rs whatsmeow-binary\src\serializer.rs whatsmeow-binary\src\token.rs whatsmeow-binary\src\tests.rs whatsmeow-binary\src\edge_case_tests.rs whatsmeow-binary\src\jid_tests.rs whatsmeow-binary\src\protocol_tests.rs whatsmeow-binary\src\binary_format_tests.rs whatsmeow-binary\src\error_handling_tests.rs whatsmeow-binary\src\performance_tests.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\whatsmeow_binary-f924d20f6ffe5a0c.d: whatsmeow-binary\src\lib.rs whatsmeow-binary\src\error.rs whatsmeow-binary\src\node.rs whatsmeow-binary\src\parser.rs whatsmeow-binary\src\serializer.rs whatsmeow-binary\src\token.rs whatsmeow-binary\src\tests.rs whatsmeow-binary\src\edge_case_tests.rs whatsmeow-binary\src\jid_tests.rs whatsmeow-binary\src\protocol_tests.rs whatsmeow-binary\src\binary_format_tests.rs whatsmeow-binary\src\error_handling_tests.rs whatsmeow-binary\src\performance_tests.rs Cargo.toml

whatsmeow-binary\src\lib.rs:
whatsmeow-binary\src\error.rs:
whatsmeow-binary\src\node.rs:
whatsmeow-binary\src\parser.rs:
whatsmeow-binary\src\serializer.rs:
whatsmeow-binary\src\token.rs:
whatsmeow-binary\src\tests.rs:
whatsmeow-binary\src\edge_case_tests.rs:
whatsmeow-binary\src\jid_tests.rs:
whatsmeow-binary\src\protocol_tests.rs:
whatsmeow-binary\src\binary_format_tests.rs:
whatsmeow-binary\src\error_handling_tests.rs:
whatsmeow-binary\src\performance_tests.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
