<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\x25519-dalek-2.0.1\src\x25519.rs`."><title>x25519.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="x25519_dalek" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">x25519_dalek/</div>x25519.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="comment">// -*- mode: rust; -*-
<a href=#2 id=2 data-nosnippet>2</a>//
<a href=#3 id=3 data-nosnippet>3</a>// This file is part of x25519-dalek.
<a href=#4 id=4 data-nosnippet>4</a>// Copyright (c) 2017-2021 isis lovecruft
<a href=#5 id=5 data-nosnippet>5</a>// Copyright (c) 2019-2021 DebugSteven
<a href=#6 id=6 data-nosnippet>6</a>// See LICENSE for licensing information.
<a href=#7 id=7 data-nosnippet>7</a>//
<a href=#8 id=8 data-nosnippet>8</a>// Authors: <AUTHORS>
<a href=#10 id=10 data-nosnippet>10</a>// - DebugSteven &lt;<EMAIL>&gt;
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a></span><span class="doccomment">//! x25519 Diffie-Hellman key exchange
<a href=#13 id=13 data-nosnippet>13</a>//!
<a href=#14 id=14 data-nosnippet>14</a>//! This implements x25519 key exchange as specified by Mike Hamburg
<a href=#15 id=15 data-nosnippet>15</a>//! and Adam Langley in [RFC7748](https://tools.ietf.org/html/rfc7748).
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a></span><span class="kw">use </span>curve25519_dalek::{edwards::EdwardsPoint, montgomery::MontgomeryPoint, traits::IsIdentity};
<a href=#18 id=18 data-nosnippet>18</a>
<a href=#19 id=19 data-nosnippet>19</a><span class="kw">use </span>rand_core::CryptoRng;
<a href=#20 id=20 data-nosnippet>20</a><span class="kw">use </span>rand_core::RngCore;
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="attr">#[cfg(feature = <span class="string">"zeroize"</span>)]
<a href=#23 id=23 data-nosnippet>23</a></span><span class="kw">use </span>zeroize::Zeroize;
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a><span class="doccomment">/// A Diffie-Hellman public key
<a href=#26 id=26 data-nosnippet>26</a>///
<a href=#27 id=27 data-nosnippet>27</a>/// We implement `Zeroize` so that downstream consumers may derive it for `Drop`
<a href=#28 id=28 data-nosnippet>28</a>/// should they wish to erase public keys from memory.  Note that this erasure
<a href=#29 id=29 data-nosnippet>29</a>/// (in this crate) does *not* automatically happen, but either must be derived
<a href=#30 id=30 data-nosnippet>30</a>/// for Drop or explicitly called.
<a href=#31 id=31 data-nosnippet>31</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, derive(serde::Serialize, serde::Deserialize))]
<a href=#32 id=32 data-nosnippet>32</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, derive(Zeroize))]
<a href=#33 id=33 data-nosnippet>33</a>#[derive(PartialEq, Eq, Hash, Copy, Clone, Debug)]
<a href=#34 id=34 data-nosnippet>34</a></span><span class="kw">pub struct </span>PublicKey(<span class="kw">pub</span>(<span class="kw">crate</span>) MontgomeryPoint);
<a href=#35 id=35 data-nosnippet>35</a>
<a href=#36 id=36 data-nosnippet>36</a><span class="kw">impl </span>From&lt;[u8; <span class="number">32</span>]&gt; <span class="kw">for </span>PublicKey {
<a href=#37 id=37 data-nosnippet>37</a>    <span class="doccomment">/// Given a byte array, construct a x25519 `PublicKey`.
<a href=#38 id=38 data-nosnippet>38</a>    </span><span class="kw">fn </span>from(bytes: [u8; <span class="number">32</span>]) -&gt; PublicKey {
<a href=#39 id=39 data-nosnippet>39</a>        PublicKey(MontgomeryPoint(bytes))
<a href=#40 id=40 data-nosnippet>40</a>    }
<a href=#41 id=41 data-nosnippet>41</a>}
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a><span class="kw">impl </span>PublicKey {
<a href=#44 id=44 data-nosnippet>44</a>    <span class="doccomment">/// Convert this public key to a byte array.
<a href=#45 id=45 data-nosnippet>45</a>    </span><span class="attr">#[inline]
<a href=#46 id=46 data-nosnippet>46</a>    </span><span class="kw">pub fn </span>to_bytes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; [u8; <span class="number">32</span>] {
<a href=#47 id=47 data-nosnippet>47</a>        <span class="self">self</span>.<span class="number">0</span>.to_bytes()
<a href=#48 id=48 data-nosnippet>48</a>    }
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a>    <span class="doccomment">/// View this public key as a byte array.
<a href=#51 id=51 data-nosnippet>51</a>    </span><span class="attr">#[inline]
<a href=#52 id=52 data-nosnippet>52</a>    </span><span class="kw">pub fn </span>as_bytes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8; <span class="number">32</span>] {
<a href=#53 id=53 data-nosnippet>53</a>        <span class="self">self</span>.<span class="number">0</span>.as_bytes()
<a href=#54 id=54 data-nosnippet>54</a>    }
<a href=#55 id=55 data-nosnippet>55</a>}
<a href=#56 id=56 data-nosnippet>56</a>
<a href=#57 id=57 data-nosnippet>57</a><span class="kw">impl </span>AsRef&lt;[u8]&gt; <span class="kw">for </span>PublicKey {
<a href=#58 id=58 data-nosnippet>58</a>    <span class="doccomment">/// View this public key as a byte array.
<a href=#59 id=59 data-nosnippet>59</a>    </span><span class="attr">#[inline]
<a href=#60 id=60 data-nosnippet>60</a>    </span><span class="kw">fn </span>as_ref(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8] {
<a href=#61 id=61 data-nosnippet>61</a>        <span class="self">self</span>.as_bytes()
<a href=#62 id=62 data-nosnippet>62</a>    }
<a href=#63 id=63 data-nosnippet>63</a>}
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a><span class="doccomment">/// A short-lived Diffie-Hellman secret key that can only be used to compute a single
<a href=#66 id=66 data-nosnippet>66</a>/// [`SharedSecret`].
<a href=#67 id=67 data-nosnippet>67</a>///
<a href=#68 id=68 data-nosnippet>68</a>/// This type is identical to the `StaticSecret` type, except that the
<a href=#69 id=69 data-nosnippet>69</a>/// [`EphemeralSecret::diffie_hellman`] method consumes and then wipes the secret key, and there
<a href=#70 id=70 data-nosnippet>70</a>/// are no serialization methods defined.  This means that [`EphemeralSecret`]s can only be
<a href=#71 id=71 data-nosnippet>71</a>/// generated from fresh randomness where the compiler statically checks that the resulting
<a href=#72 id=72 data-nosnippet>72</a>/// secret is used at most once.
<a href=#73 id=73 data-nosnippet>73</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"zeroize"</span>, derive(Zeroize))]
<a href=#74 id=74 data-nosnippet>74</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, zeroize(drop))]
<a href=#75 id=75 data-nosnippet>75</a></span><span class="kw">pub struct </span>EphemeralSecret(<span class="kw">pub</span>(<span class="kw">crate</span>) [u8; <span class="number">32</span>]);
<a href=#76 id=76 data-nosnippet>76</a>
<a href=#77 id=77 data-nosnippet>77</a><span class="kw">impl </span>EphemeralSecret {
<a href=#78 id=78 data-nosnippet>78</a>    <span class="doccomment">/// Perform a Diffie-Hellman key agreement between `self` and
<a href=#79 id=79 data-nosnippet>79</a>    /// `their_public` key to produce a [`SharedSecret`].
<a href=#80 id=80 data-nosnippet>80</a>    </span><span class="kw">pub fn </span>diffie_hellman(<span class="self">self</span>, their_public: <span class="kw-2">&amp;</span>PublicKey) -&gt; SharedSecret {
<a href=#81 id=81 data-nosnippet>81</a>        SharedSecret(their_public.<span class="number">0</span>.mul_clamped(<span class="self">self</span>.<span class="number">0</span>))
<a href=#82 id=82 data-nosnippet>82</a>    }
<a href=#83 id=83 data-nosnippet>83</a>
<a href=#84 id=84 data-nosnippet>84</a>    <span class="doccomment">/// Generate a new [`EphemeralSecret`] with the supplied RNG.
<a href=#85 id=85 data-nosnippet>85</a>    </span><span class="attr">#[deprecated(
<a href=#86 id=86 data-nosnippet>86</a>        since = <span class="string">"2.0.0"</span>,
<a href=#87 id=87 data-nosnippet>87</a>        note = <span class="string">"Renamed to `random_from_rng`. This will be removed in 2.1.0"
<a href=#88 id=88 data-nosnippet>88</a>    </span>)]
<a href=#89 id=89 data-nosnippet>89</a>    </span><span class="kw">pub fn </span>new&lt;T: RngCore + CryptoRng&gt;(<span class="kw-2">mut </span>csprng: T) -&gt; <span class="self">Self </span>{
<a href=#90 id=90 data-nosnippet>90</a>        <span class="self">Self</span>::random_from_rng(<span class="kw-2">&amp;mut </span>csprng)
<a href=#91 id=91 data-nosnippet>91</a>    }
<a href=#92 id=92 data-nosnippet>92</a>
<a href=#93 id=93 data-nosnippet>93</a>    <span class="doccomment">/// Generate a new [`EphemeralSecret`] with the supplied RNG.
<a href=#94 id=94 data-nosnippet>94</a>    </span><span class="kw">pub fn </span>random_from_rng&lt;T: RngCore + CryptoRng&gt;(<span class="kw-2">mut </span>csprng: T) -&gt; <span class="self">Self </span>{
<a href=#95 id=95 data-nosnippet>95</a>        <span class="comment">// The secret key is random bytes. Clamping is done later.
<a href=#96 id=96 data-nosnippet>96</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>bytes = [<span class="number">0u8</span>; <span class="number">32</span>];
<a href=#97 id=97 data-nosnippet>97</a>        csprng.fill_bytes(<span class="kw-2">&amp;mut </span>bytes);
<a href=#98 id=98 data-nosnippet>98</a>        EphemeralSecret(bytes)
<a href=#99 id=99 data-nosnippet>99</a>    }
<a href=#100 id=100 data-nosnippet>100</a>
<a href=#101 id=101 data-nosnippet>101</a>    <span class="doccomment">/// Generate a new [`EphemeralSecret`].
<a href=#102 id=102 data-nosnippet>102</a>    </span><span class="attr">#[cfg(feature = <span class="string">"getrandom"</span>)]
<a href=#103 id=103 data-nosnippet>103</a>    </span><span class="kw">pub fn </span>random() -&gt; <span class="self">Self </span>{
<a href=#104 id=104 data-nosnippet>104</a>        <span class="self">Self</span>::random_from_rng(rand_core::OsRng)
<a href=#105 id=105 data-nosnippet>105</a>    }
<a href=#106 id=106 data-nosnippet>106</a>}
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a><span class="kw">impl</span>&lt;<span class="lifetime">'a</span>&gt; From&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>EphemeralSecret&gt; <span class="kw">for </span>PublicKey {
<a href=#109 id=109 data-nosnippet>109</a>    <span class="doccomment">/// Given an x25519 [`EphemeralSecret`] key, compute its corresponding [`PublicKey`].
<a href=#110 id=110 data-nosnippet>110</a>    </span><span class="kw">fn </span>from(secret: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>EphemeralSecret) -&gt; PublicKey {
<a href=#111 id=111 data-nosnippet>111</a>        PublicKey(EdwardsPoint::mul_base_clamped(secret.<span class="number">0</span>).to_montgomery())
<a href=#112 id=112 data-nosnippet>112</a>    }
<a href=#113 id=113 data-nosnippet>113</a>}
<a href=#114 id=114 data-nosnippet>114</a>
<a href=#115 id=115 data-nosnippet>115</a><span class="doccomment">/// A Diffie-Hellman secret key which may be used more than once, but is
<a href=#116 id=116 data-nosnippet>116</a>/// purposefully not serialiseable in order to discourage key-reuse.  This is
<a href=#117 id=117 data-nosnippet>117</a>/// implemented to facilitate protocols such as Noise (e.g. Noise IK key usage,
<a href=#118 id=118 data-nosnippet>118</a>/// etc.) and X3DH which require an "ephemeral" key to conduct the
<a href=#119 id=119 data-nosnippet>119</a>/// Diffie-Hellman operation multiple times throughout the protocol, while the
<a href=#120 id=120 data-nosnippet>120</a>/// protocol run at a higher level is only conducted once per key.
<a href=#121 id=121 data-nosnippet>121</a>///
<a href=#122 id=122 data-nosnippet>122</a>/// Similarly to [`EphemeralSecret`], this type does _not_ have serialisation
<a href=#123 id=123 data-nosnippet>123</a>/// methods, in order to discourage long-term usage of secret key material. (For
<a href=#124 id=124 data-nosnippet>124</a>/// long-term secret keys, see `StaticSecret`.)
<a href=#125 id=125 data-nosnippet>125</a>///
<a href=#126 id=126 data-nosnippet>126</a>/// # Warning
<a href=#127 id=127 data-nosnippet>127</a>///
<a href=#128 id=128 data-nosnippet>128</a>/// If you're uncertain about whether you should use this, then you likely
<a href=#129 id=129 data-nosnippet>129</a>/// should not be using this.  Our strongly recommended advice is to use
<a href=#130 id=130 data-nosnippet>130</a>/// [`EphemeralSecret`] at all times, as that type enforces at compile-time that
<a href=#131 id=131 data-nosnippet>131</a>/// secret keys are never reused, which can have very serious security
<a href=#132 id=132 data-nosnippet>132</a>/// implications for many protocols.
<a href=#133 id=133 data-nosnippet>133</a></span><span class="attr">#[cfg(feature = <span class="string">"reusable_secrets"</span>)]
<a href=#134 id=134 data-nosnippet>134</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, derive(Zeroize))]
<a href=#135 id=135 data-nosnippet>135</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, zeroize(drop))]
<a href=#136 id=136 data-nosnippet>136</a>#[derive(Clone)]
<a href=#137 id=137 data-nosnippet>137</a></span><span class="kw">pub struct </span>ReusableSecret(<span class="kw">pub</span>(<span class="kw">crate</span>) [u8; <span class="number">32</span>]);
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a><span class="attr">#[cfg(feature = <span class="string">"reusable_secrets"</span>)]
<a href=#140 id=140 data-nosnippet>140</a></span><span class="kw">impl </span>ReusableSecret {
<a href=#141 id=141 data-nosnippet>141</a>    <span class="doccomment">/// Perform a Diffie-Hellman key agreement between `self` and
<a href=#142 id=142 data-nosnippet>142</a>    /// `their_public` key to produce a [`SharedSecret`].
<a href=#143 id=143 data-nosnippet>143</a>    </span><span class="kw">pub fn </span>diffie_hellman(<span class="kw-2">&amp;</span><span class="self">self</span>, their_public: <span class="kw-2">&amp;</span>PublicKey) -&gt; SharedSecret {
<a href=#144 id=144 data-nosnippet>144</a>        SharedSecret(their_public.<span class="number">0</span>.mul_clamped(<span class="self">self</span>.<span class="number">0</span>))
<a href=#145 id=145 data-nosnippet>145</a>    }
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>    <span class="doccomment">/// Generate a new [`ReusableSecret`] with the supplied RNG.
<a href=#148 id=148 data-nosnippet>148</a>    </span><span class="attr">#[deprecated(
<a href=#149 id=149 data-nosnippet>149</a>        since = <span class="string">"2.0.0"</span>,
<a href=#150 id=150 data-nosnippet>150</a>        note = <span class="string">"Renamed to `random_from_rng`. This will be removed in 2.1.0."
<a href=#151 id=151 data-nosnippet>151</a>    </span>)]
<a href=#152 id=152 data-nosnippet>152</a>    </span><span class="kw">pub fn </span>new&lt;T: RngCore + CryptoRng&gt;(<span class="kw-2">mut </span>csprng: T) -&gt; <span class="self">Self </span>{
<a href=#153 id=153 data-nosnippet>153</a>        <span class="self">Self</span>::random_from_rng(<span class="kw-2">&amp;mut </span>csprng)
<a href=#154 id=154 data-nosnippet>154</a>    }
<a href=#155 id=155 data-nosnippet>155</a>
<a href=#156 id=156 data-nosnippet>156</a>    <span class="doccomment">/// Generate a new [`ReusableSecret`] with the supplied RNG.
<a href=#157 id=157 data-nosnippet>157</a>    </span><span class="kw">pub fn </span>random_from_rng&lt;T: RngCore + CryptoRng&gt;(<span class="kw-2">mut </span>csprng: T) -&gt; <span class="self">Self </span>{
<a href=#158 id=158 data-nosnippet>158</a>        <span class="comment">// The secret key is random bytes. Clamping is done later.
<a href=#159 id=159 data-nosnippet>159</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>bytes = [<span class="number">0u8</span>; <span class="number">32</span>];
<a href=#160 id=160 data-nosnippet>160</a>        csprng.fill_bytes(<span class="kw-2">&amp;mut </span>bytes);
<a href=#161 id=161 data-nosnippet>161</a>        ReusableSecret(bytes)
<a href=#162 id=162 data-nosnippet>162</a>    }
<a href=#163 id=163 data-nosnippet>163</a>
<a href=#164 id=164 data-nosnippet>164</a>    <span class="doccomment">/// Generate a new [`ReusableSecret`].
<a href=#165 id=165 data-nosnippet>165</a>    </span><span class="attr">#[cfg(feature = <span class="string">"getrandom"</span>)]
<a href=#166 id=166 data-nosnippet>166</a>    </span><span class="kw">pub fn </span>random() -&gt; <span class="self">Self </span>{
<a href=#167 id=167 data-nosnippet>167</a>        <span class="self">Self</span>::random_from_rng(rand_core::OsRng)
<a href=#168 id=168 data-nosnippet>168</a>    }
<a href=#169 id=169 data-nosnippet>169</a>}
<a href=#170 id=170 data-nosnippet>170</a>
<a href=#171 id=171 data-nosnippet>171</a><span class="attr">#[cfg(feature = <span class="string">"reusable_secrets"</span>)]
<a href=#172 id=172 data-nosnippet>172</a></span><span class="kw">impl</span>&lt;<span class="lifetime">'a</span>&gt; From&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>ReusableSecret&gt; <span class="kw">for </span>PublicKey {
<a href=#173 id=173 data-nosnippet>173</a>    <span class="doccomment">/// Given an x25519 [`ReusableSecret`] key, compute its corresponding [`PublicKey`].
<a href=#174 id=174 data-nosnippet>174</a>    </span><span class="kw">fn </span>from(secret: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>ReusableSecret) -&gt; PublicKey {
<a href=#175 id=175 data-nosnippet>175</a>        PublicKey(EdwardsPoint::mul_base_clamped(secret.<span class="number">0</span>).to_montgomery())
<a href=#176 id=176 data-nosnippet>176</a>    }
<a href=#177 id=177 data-nosnippet>177</a>}
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a><span class="doccomment">/// A Diffie-Hellman secret key that can be used to compute multiple [`SharedSecret`]s.
<a href=#180 id=180 data-nosnippet>180</a>///
<a href=#181 id=181 data-nosnippet>181</a>/// This type is identical to the [`EphemeralSecret`] type, except that the
<a href=#182 id=182 data-nosnippet>182</a>/// [`StaticSecret::diffie_hellman`] method does not consume the secret key, and the type provides
<a href=#183 id=183 data-nosnippet>183</a>/// serialization methods to save and load key material.  This means that the secret may be used
<a href=#184 id=184 data-nosnippet>184</a>/// multiple times (but does not *have to be*).
<a href=#185 id=185 data-nosnippet>185</a>///
<a href=#186 id=186 data-nosnippet>186</a>/// # Warning
<a href=#187 id=187 data-nosnippet>187</a>///
<a href=#188 id=188 data-nosnippet>188</a>/// If you're uncertain about whether you should use this, then you likely
<a href=#189 id=189 data-nosnippet>189</a>/// should not be using this.  Our strongly recommended advice is to use
<a href=#190 id=190 data-nosnippet>190</a>/// [`EphemeralSecret`] at all times, as that type enforces at compile-time that
<a href=#191 id=191 data-nosnippet>191</a>/// secret keys are never reused, which can have very serious security
<a href=#192 id=192 data-nosnippet>192</a>/// implications for many protocols.
<a href=#193 id=193 data-nosnippet>193</a></span><span class="attr">#[cfg(feature = <span class="string">"static_secrets"</span>)]
<a href=#194 id=194 data-nosnippet>194</a>#[cfg_attr(feature = <span class="string">"serde"</span>, derive(serde::Serialize, serde::Deserialize))]
<a href=#195 id=195 data-nosnippet>195</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, derive(Zeroize))]
<a href=#196 id=196 data-nosnippet>196</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, zeroize(drop))]
<a href=#197 id=197 data-nosnippet>197</a>#[derive(Clone)]
<a href=#198 id=198 data-nosnippet>198</a></span><span class="kw">pub struct </span>StaticSecret([u8; <span class="number">32</span>]);
<a href=#199 id=199 data-nosnippet>199</a>
<a href=#200 id=200 data-nosnippet>200</a><span class="attr">#[cfg(feature = <span class="string">"static_secrets"</span>)]
<a href=#201 id=201 data-nosnippet>201</a></span><span class="kw">impl </span>StaticSecret {
<a href=#202 id=202 data-nosnippet>202</a>    <span class="doccomment">/// Perform a Diffie-Hellman key agreement between `self` and
<a href=#203 id=203 data-nosnippet>203</a>    /// `their_public` key to produce a `SharedSecret`.
<a href=#204 id=204 data-nosnippet>204</a>    </span><span class="kw">pub fn </span>diffie_hellman(<span class="kw-2">&amp;</span><span class="self">self</span>, their_public: <span class="kw-2">&amp;</span>PublicKey) -&gt; SharedSecret {
<a href=#205 id=205 data-nosnippet>205</a>        SharedSecret(their_public.<span class="number">0</span>.mul_clamped(<span class="self">self</span>.<span class="number">0</span>))
<a href=#206 id=206 data-nosnippet>206</a>    }
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>    <span class="doccomment">/// Generate a new [`StaticSecret`] with the supplied RNG.
<a href=#209 id=209 data-nosnippet>209</a>    </span><span class="attr">#[deprecated(
<a href=#210 id=210 data-nosnippet>210</a>        since = <span class="string">"2.0.0"</span>,
<a href=#211 id=211 data-nosnippet>211</a>        note = <span class="string">"Renamed to `random_from_rng`. This will be removed in 2.1.0"
<a href=#212 id=212 data-nosnippet>212</a>    </span>)]
<a href=#213 id=213 data-nosnippet>213</a>    </span><span class="kw">pub fn </span>new&lt;T: RngCore + CryptoRng&gt;(<span class="kw-2">mut </span>csprng: T) -&gt; <span class="self">Self </span>{
<a href=#214 id=214 data-nosnippet>214</a>        <span class="self">Self</span>::random_from_rng(<span class="kw-2">&amp;mut </span>csprng)
<a href=#215 id=215 data-nosnippet>215</a>    }
<a href=#216 id=216 data-nosnippet>216</a>
<a href=#217 id=217 data-nosnippet>217</a>    <span class="doccomment">/// Generate a new [`StaticSecret`] with the supplied RNG.
<a href=#218 id=218 data-nosnippet>218</a>    </span><span class="kw">pub fn </span>random_from_rng&lt;T: RngCore + CryptoRng&gt;(<span class="kw-2">mut </span>csprng: T) -&gt; <span class="self">Self </span>{
<a href=#219 id=219 data-nosnippet>219</a>        <span class="comment">// The secret key is random bytes. Clamping is done later.
<a href=#220 id=220 data-nosnippet>220</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>bytes = [<span class="number">0u8</span>; <span class="number">32</span>];
<a href=#221 id=221 data-nosnippet>221</a>        csprng.fill_bytes(<span class="kw-2">&amp;mut </span>bytes);
<a href=#222 id=222 data-nosnippet>222</a>        StaticSecret(bytes)
<a href=#223 id=223 data-nosnippet>223</a>    }
<a href=#224 id=224 data-nosnippet>224</a>
<a href=#225 id=225 data-nosnippet>225</a>    <span class="doccomment">/// Generate a new [`StaticSecret`].
<a href=#226 id=226 data-nosnippet>226</a>    </span><span class="attr">#[cfg(feature = <span class="string">"getrandom"</span>)]
<a href=#227 id=227 data-nosnippet>227</a>    </span><span class="kw">pub fn </span>random() -&gt; <span class="self">Self </span>{
<a href=#228 id=228 data-nosnippet>228</a>        <span class="self">Self</span>::random_from_rng(rand_core::OsRng)
<a href=#229 id=229 data-nosnippet>229</a>    }
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>    <span class="doccomment">/// Extract this key's bytes for serialization.
<a href=#232 id=232 data-nosnippet>232</a>    </span><span class="attr">#[inline]
<a href=#233 id=233 data-nosnippet>233</a>    </span><span class="kw">pub fn </span>to_bytes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; [u8; <span class="number">32</span>] {
<a href=#234 id=234 data-nosnippet>234</a>        <span class="self">self</span>.<span class="number">0
<a href=#235 id=235 data-nosnippet>235</a>    </span>}
<a href=#236 id=236 data-nosnippet>236</a>
<a href=#237 id=237 data-nosnippet>237</a>    <span class="doccomment">/// View this key as a byte array.
<a href=#238 id=238 data-nosnippet>238</a>    </span><span class="attr">#[inline]
<a href=#239 id=239 data-nosnippet>239</a>    </span><span class="kw">pub fn </span>as_bytes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8; <span class="number">32</span>] {
<a href=#240 id=240 data-nosnippet>240</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.<span class="number">0
<a href=#241 id=241 data-nosnippet>241</a>    </span>}
<a href=#242 id=242 data-nosnippet>242</a>}
<a href=#243 id=243 data-nosnippet>243</a>
<a href=#244 id=244 data-nosnippet>244</a><span class="attr">#[cfg(feature = <span class="string">"static_secrets"</span>)]
<a href=#245 id=245 data-nosnippet>245</a></span><span class="kw">impl </span>From&lt;[u8; <span class="number">32</span>]&gt; <span class="kw">for </span>StaticSecret {
<a href=#246 id=246 data-nosnippet>246</a>    <span class="doccomment">/// Load a secret key from a byte array.
<a href=#247 id=247 data-nosnippet>247</a>    </span><span class="kw">fn </span>from(bytes: [u8; <span class="number">32</span>]) -&gt; StaticSecret {
<a href=#248 id=248 data-nosnippet>248</a>        StaticSecret(bytes)
<a href=#249 id=249 data-nosnippet>249</a>    }
<a href=#250 id=250 data-nosnippet>250</a>}
<a href=#251 id=251 data-nosnippet>251</a>
<a href=#252 id=252 data-nosnippet>252</a><span class="attr">#[cfg(feature = <span class="string">"static_secrets"</span>)]
<a href=#253 id=253 data-nosnippet>253</a></span><span class="kw">impl</span>&lt;<span class="lifetime">'a</span>&gt; From&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>StaticSecret&gt; <span class="kw">for </span>PublicKey {
<a href=#254 id=254 data-nosnippet>254</a>    <span class="doccomment">/// Given an x25519 [`StaticSecret`] key, compute its corresponding [`PublicKey`].
<a href=#255 id=255 data-nosnippet>255</a>    </span><span class="kw">fn </span>from(secret: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>StaticSecret) -&gt; PublicKey {
<a href=#256 id=256 data-nosnippet>256</a>        PublicKey(EdwardsPoint::mul_base_clamped(secret.<span class="number">0</span>).to_montgomery())
<a href=#257 id=257 data-nosnippet>257</a>    }
<a href=#258 id=258 data-nosnippet>258</a>}
<a href=#259 id=259 data-nosnippet>259</a>
<a href=#260 id=260 data-nosnippet>260</a><span class="attr">#[cfg(feature = <span class="string">"static_secrets"</span>)]
<a href=#261 id=261 data-nosnippet>261</a></span><span class="kw">impl </span>AsRef&lt;[u8]&gt; <span class="kw">for </span>StaticSecret {
<a href=#262 id=262 data-nosnippet>262</a>    <span class="doccomment">/// View this key as a byte array.
<a href=#263 id=263 data-nosnippet>263</a>    </span><span class="attr">#[inline]
<a href=#264 id=264 data-nosnippet>264</a>    </span><span class="kw">fn </span>as_ref(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8] {
<a href=#265 id=265 data-nosnippet>265</a>        <span class="self">self</span>.as_bytes()
<a href=#266 id=266 data-nosnippet>266</a>    }
<a href=#267 id=267 data-nosnippet>267</a>}
<a href=#268 id=268 data-nosnippet>268</a>
<a href=#269 id=269 data-nosnippet>269</a><span class="doccomment">/// The result of a Diffie-Hellman key exchange.
<a href=#270 id=270 data-nosnippet>270</a>///
<a href=#271 id=271 data-nosnippet>271</a>/// Each party computes this using their [`EphemeralSecret`] or [`StaticSecret`] and their
<a href=#272 id=272 data-nosnippet>272</a>/// counterparty's [`PublicKey`].
<a href=#273 id=273 data-nosnippet>273</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"zeroize"</span>, derive(Zeroize))]
<a href=#274 id=274 data-nosnippet>274</a>#[cfg_attr(feature = <span class="string">"zeroize"</span>, zeroize(drop))]
<a href=#275 id=275 data-nosnippet>275</a></span><span class="kw">pub struct </span>SharedSecret(<span class="kw">pub</span>(<span class="kw">crate</span>) MontgomeryPoint);
<a href=#276 id=276 data-nosnippet>276</a>
<a href=#277 id=277 data-nosnippet>277</a><span class="kw">impl </span>SharedSecret {
<a href=#278 id=278 data-nosnippet>278</a>    <span class="doccomment">/// Convert this shared secret to a byte array.
<a href=#279 id=279 data-nosnippet>279</a>    </span><span class="attr">#[inline]
<a href=#280 id=280 data-nosnippet>280</a>    </span><span class="kw">pub fn </span>to_bytes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; [u8; <span class="number">32</span>] {
<a href=#281 id=281 data-nosnippet>281</a>        <span class="self">self</span>.<span class="number">0</span>.to_bytes()
<a href=#282 id=282 data-nosnippet>282</a>    }
<a href=#283 id=283 data-nosnippet>283</a>
<a href=#284 id=284 data-nosnippet>284</a>    <span class="doccomment">/// View this shared secret key as a byte array.
<a href=#285 id=285 data-nosnippet>285</a>    </span><span class="attr">#[inline]
<a href=#286 id=286 data-nosnippet>286</a>    </span><span class="kw">pub fn </span>as_bytes(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8; <span class="number">32</span>] {
<a href=#287 id=287 data-nosnippet>287</a>        <span class="self">self</span>.<span class="number">0</span>.as_bytes()
<a href=#288 id=288 data-nosnippet>288</a>    }
<a href=#289 id=289 data-nosnippet>289</a>
<a href=#290 id=290 data-nosnippet>290</a>    <span class="doccomment">/// Ensure in constant-time that this shared secret did not result from a
<a href=#291 id=291 data-nosnippet>291</a>    /// key exchange with non-contributory behaviour.
<a href=#292 id=292 data-nosnippet>292</a>    ///
<a href=#293 id=293 data-nosnippet>293</a>    /// In some more exotic protocols which need to guarantee "contributory"
<a href=#294 id=294 data-nosnippet>294</a>    /// behaviour for both parties, that is, that each party contributed a public
<a href=#295 id=295 data-nosnippet>295</a>    /// value which increased the security of the resulting shared secret.
<a href=#296 id=296 data-nosnippet>296</a>    /// To take an example protocol attack where this could lead to undesirable
<a href=#297 id=297 data-nosnippet>297</a>    /// results [from Thái "thaidn" Dương](https://vnhacker.blogspot.com/2015/09/why-not-validating-curve25519-public.html):
<a href=#298 id=298 data-nosnippet>298</a>    ///
<a href=#299 id=299 data-nosnippet>299</a>    /// &gt; If Mallory replaces Alice's and Bob's public keys with zero, which is
<a href=#300 id=300 data-nosnippet>300</a>    /// &gt; a valid Curve25519 public key, he would be able to force the ECDH
<a href=#301 id=301 data-nosnippet>301</a>    /// &gt; shared value to be zero, which is the encoding of the point at infinity,
<a href=#302 id=302 data-nosnippet>302</a>    /// &gt; and thus get to dictate some publicly known values as the shared
<a href=#303 id=303 data-nosnippet>303</a>    /// &gt; keys. It still requires an active man-in-the-middle attack to pull the
<a href=#304 id=304 data-nosnippet>304</a>    /// &gt; trick, after which, however, not only Mallory can decode Alice's data,
<a href=#305 id=305 data-nosnippet>305</a>    /// &gt; but everyone too! It is also impossible for Alice and Bob to detect the
<a href=#306 id=306 data-nosnippet>306</a>    /// &gt; intrusion, as they still share the same keys, and can communicate with
<a href=#307 id=307 data-nosnippet>307</a>    /// &gt; each other as normal.
<a href=#308 id=308 data-nosnippet>308</a>    ///
<a href=#309 id=309 data-nosnippet>309</a>    /// The original Curve25519 specification argues that checks for
<a href=#310 id=310 data-nosnippet>310</a>    /// non-contributory behaviour are "unnecessary for Diffie-Hellman".
<a href=#311 id=311 data-nosnippet>311</a>    /// Whether this check is necessary for any particular given protocol is
<a href=#312 id=312 data-nosnippet>312</a>    /// often a matter of debate, which we will not re-hash here, but simply
<a href=#313 id=313 data-nosnippet>313</a>    /// cite some of the [relevant] [public] [discussions].
<a href=#314 id=314 data-nosnippet>314</a>    ///
<a href=#315 id=315 data-nosnippet>315</a>    /// # Returns
<a href=#316 id=316 data-nosnippet>316</a>    ///
<a href=#317 id=317 data-nosnippet>317</a>    /// Returns `true` if the key exchange was contributory (good), and `false`
<a href=#318 id=318 data-nosnippet>318</a>    /// otherwise (can be bad for some protocols).
<a href=#319 id=319 data-nosnippet>319</a>    ///
<a href=#320 id=320 data-nosnippet>320</a>    /// [relevant]: https://tools.ietf.org/html/rfc7748#page-15
<a href=#321 id=321 data-nosnippet>321</a>    /// [public]: https://vnhacker.blogspot.com/2015/09/why-not-validating-curve25519-public.html
<a href=#322 id=322 data-nosnippet>322</a>    /// [discussions]: https://vnhacker.blogspot.com/2016/08/the-internet-of-broken-protocols.html
<a href=#323 id=323 data-nosnippet>323</a>    </span><span class="attr">#[must_use]
<a href=#324 id=324 data-nosnippet>324</a>    </span><span class="kw">pub fn </span>was_contributory(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#325 id=325 data-nosnippet>325</a>        !<span class="self">self</span>.<span class="number">0</span>.is_identity()
<a href=#326 id=326 data-nosnippet>326</a>    }
<a href=#327 id=327 data-nosnippet>327</a>}
<a href=#328 id=328 data-nosnippet>328</a>
<a href=#329 id=329 data-nosnippet>329</a><span class="kw">impl </span>AsRef&lt;[u8]&gt; <span class="kw">for </span>SharedSecret {
<a href=#330 id=330 data-nosnippet>330</a>    <span class="doccomment">/// View this shared secret key as a byte array.
<a href=#331 id=331 data-nosnippet>331</a>    </span><span class="attr">#[inline]
<a href=#332 id=332 data-nosnippet>332</a>    </span><span class="kw">fn </span>as_ref(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8] {
<a href=#333 id=333 data-nosnippet>333</a>        <span class="self">self</span>.as_bytes()
<a href=#334 id=334 data-nosnippet>334</a>    }
<a href=#335 id=335 data-nosnippet>335</a>}
<a href=#336 id=336 data-nosnippet>336</a>
<a href=#337 id=337 data-nosnippet>337</a><span class="doccomment">/// The bare, byte-oriented x25519 function, exactly as specified in RFC7748.
<a href=#338 id=338 data-nosnippet>338</a>///
<a href=#339 id=339 data-nosnippet>339</a>/// This can be used with [`X25519_BASEPOINT_BYTES`] for people who
<a href=#340 id=340 data-nosnippet>340</a>/// cannot use the better, safer, and faster ephemeral DH API.
<a href=#341 id=341 data-nosnippet>341</a>///
<a href=#342 id=342 data-nosnippet>342</a>/// # Example
<a href=#343 id=343 data-nosnippet>343</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"static_secrets"</span>, doc = <span class="string">"```"</span>)]
<a href=#344 id=344 data-nosnippet>344</a>#[cfg_attr(not(feature = <span class="string">"static_secrets"</span>), doc = <span class="string">"```ignore"</span>)]
<a href=#345 id=345 data-nosnippet>345</a></span><span class="doccomment">/// use rand_core::OsRng;
<a href=#346 id=346 data-nosnippet>346</a>/// use rand_core::RngCore;
<a href=#347 id=347 data-nosnippet>347</a>///
<a href=#348 id=348 data-nosnippet>348</a>/// use x25519_dalek::x25519;
<a href=#349 id=349 data-nosnippet>349</a>/// use x25519_dalek::StaticSecret;
<a href=#350 id=350 data-nosnippet>350</a>/// use x25519_dalek::PublicKey;
<a href=#351 id=351 data-nosnippet>351</a>///
<a href=#352 id=352 data-nosnippet>352</a>/// // Generate Alice's key pair.
<a href=#353 id=353 data-nosnippet>353</a>/// let alice_secret = StaticSecret::random_from_rng(&amp;mut OsRng);
<a href=#354 id=354 data-nosnippet>354</a>/// let alice_public = PublicKey::from(&amp;alice_secret);
<a href=#355 id=355 data-nosnippet>355</a>///
<a href=#356 id=356 data-nosnippet>356</a>/// // Generate Bob's key pair.
<a href=#357 id=357 data-nosnippet>357</a>/// let bob_secret = StaticSecret::random_from_rng(&amp;mut OsRng);
<a href=#358 id=358 data-nosnippet>358</a>/// let bob_public = PublicKey::from(&amp;bob_secret);
<a href=#359 id=359 data-nosnippet>359</a>///
<a href=#360 id=360 data-nosnippet>360</a>/// // Alice and Bob should now exchange their public keys.
<a href=#361 id=361 data-nosnippet>361</a>///
<a href=#362 id=362 data-nosnippet>362</a>/// // Once they've done so, they may generate a shared secret.
<a href=#363 id=363 data-nosnippet>363</a>/// let alice_shared = x25519(alice_secret.to_bytes(), bob_public.to_bytes());
<a href=#364 id=364 data-nosnippet>364</a>/// let bob_shared = x25519(bob_secret.to_bytes(), alice_public.to_bytes());
<a href=#365 id=365 data-nosnippet>365</a>///
<a href=#366 id=366 data-nosnippet>366</a>/// assert_eq!(alice_shared, bob_shared);
<a href=#367 id=367 data-nosnippet>367</a>/// ```
<a href=#368 id=368 data-nosnippet>368</a></span><span class="kw">pub fn </span>x25519(k: [u8; <span class="number">32</span>], u: [u8; <span class="number">32</span>]) -&gt; [u8; <span class="number">32</span>] {
<a href=#369 id=369 data-nosnippet>369</a>    MontgomeryPoint(u).mul_clamped(k).to_bytes()
<a href=#370 id=370 data-nosnippet>370</a>}
<a href=#371 id=371 data-nosnippet>371</a>
<a href=#372 id=372 data-nosnippet>372</a><span class="doccomment">/// The X25519 basepoint, for use with the bare, byte-oriented x25519
<a href=#373 id=373 data-nosnippet>373</a>/// function.  This is provided for people who cannot use the typed
<a href=#374 id=374 data-nosnippet>374</a>/// DH API for some reason.
<a href=#375 id=375 data-nosnippet>375</a></span><span class="kw">pub const </span>X25519_BASEPOINT_BYTES: [u8; <span class="number">32</span>] = [
<a href=#376 id=376 data-nosnippet>376</a>    <span class="number">9</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>,
<a href=#377 id=377 data-nosnippet>377</a>];</code></pre></div></section></main></body></html>