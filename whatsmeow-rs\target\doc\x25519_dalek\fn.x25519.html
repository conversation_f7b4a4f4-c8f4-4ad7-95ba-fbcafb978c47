<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="The bare, byte-oriented x25519 function, exactly as specified in RFC7748."><title>x25519 in x25519_dalek - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="x25519_dalek" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../static.files/storage-82c7156e.js"></script><script defer src="sidebar-items.js"></script><script defer src="../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc fn"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button><a class="logo-container" href="../x25519_dalek/index.html"><img src="https://cdn.jsdelivr.net/gh/dalek-cryptography/curve25519-dalek/docs/assets/dalek-logo-clear.png" alt=""></a></nav><nav class="sidebar"><div class="sidebar-crate"><a class="logo-container" href="../x25519_dalek/index.html"><img src="https://cdn.jsdelivr.net/gh/dalek-cryptography/curve25519-dalek/docs/assets/dalek-logo-clear.png" alt="logo"></a><h2><a href="../x25519_dalek/index.html">x25519_<wbr>dalek</a><span class="version">2.0.1</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">x25519</a></h2><h3><a href="#">Sections</a></h3><ul class="block top-toc"><li><a href="#example" title="Example">Example</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="index.html">In crate x25519_<wbr>dalek</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="index.html">x25519_dalek</a></div><h1>Function <span class="fn">x25519</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/x25519_dalek/x25519.rs.html#368-370">Source</a> </span></div><pre class="rust item-decl"><code>pub fn x25519(k: [<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>], u: [<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>]) -&gt; [<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>]</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>The bare, byte-oriented x25519 function, exactly as specified in RFC7748.</p>
<p>This can be used with <a href="constant.X25519_BASEPOINT_BYTES.html" title="constant x25519_dalek::X25519_BASEPOINT_BYTES"><code>X25519_BASEPOINT_BYTES</code></a> for people who
cannot use the better, safer, and faster ephemeral DH API.</p>
<h2 id="example"><a class="doc-anchor" href="#example">§</a>Example</h2>
<div class="example-wrap ignore"><a href="#" class="tooltip" title="This example is not tested">ⓘ</a><pre class="rust rust-example-rendered"><code><span class="kw">use </span>rand_core::OsRng;
<span class="kw">use </span>rand_core::RngCore;

<span class="kw">use </span>x25519_dalek::x25519;
<span class="kw">use </span>x25519_dalek::StaticSecret;
<span class="kw">use </span>x25519_dalek::PublicKey;

<span class="comment">// Generate Alice's key pair.
</span><span class="kw">let </span>alice_secret = StaticSecret::random_from_rng(<span class="kw-2">&amp;mut </span>OsRng);
<span class="kw">let </span>alice_public = PublicKey::from(<span class="kw-2">&amp;</span>alice_secret);

<span class="comment">// Generate Bob's key pair.
</span><span class="kw">let </span>bob_secret = StaticSecret::random_from_rng(<span class="kw-2">&amp;mut </span>OsRng);
<span class="kw">let </span>bob_public = PublicKey::from(<span class="kw-2">&amp;</span>bob_secret);

<span class="comment">// Alice and Bob should now exchange their public keys.

// Once they've done so, they may generate a shared secret.
</span><span class="kw">let </span>alice_shared = x25519(alice_secret.to_bytes(), bob_public.to_bytes());
<span class="kw">let </span>bob_shared = x25519(bob_secret.to_bytes(), alice_public.to_bytes());

<span class="macro">assert_eq!</span>(alice_shared, bob_shared);</code></pre></div>
</div></details></section></div></main></body></html>