(function() {
    var implementors = Object.fromEntries([["x25519_dalek",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;[<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.87.0/std/primitive.u8.html\">u8</a>; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.87.0/std/primitive.array.html\">32</a>]&gt; for <a class=\"struct\" href=\"x25519_dalek/struct.PublicKey.html\" title=\"struct x25519_dalek::PublicKey\">PublicKey</a>"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;&amp;'a <a class=\"struct\" href=\"x25519_dalek/struct.EphemeralSecret.html\" title=\"struct x25519_dalek::EphemeralSecret\">EphemeralSecret</a>&gt; for <a class=\"struct\" href=\"x25519_dalek/struct.PublicKey.html\" title=\"struct x25519_dalek::PublicKey\">PublicKey</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[915]}