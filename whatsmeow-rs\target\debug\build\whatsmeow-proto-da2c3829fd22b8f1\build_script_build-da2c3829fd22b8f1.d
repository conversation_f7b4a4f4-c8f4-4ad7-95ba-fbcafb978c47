D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\build\whatsmeow-proto-da2c3829fd22b8f1\build_script_build-da2c3829fd22b8f1.exe: whatsmeow-proto\build.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\build\whatsmeow-proto-da2c3829fd22b8f1\build_script_build-da2c3829fd22b8f1.d: whatsmeow-proto\build.rs Cargo.toml

whatsmeow-proto\build.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
