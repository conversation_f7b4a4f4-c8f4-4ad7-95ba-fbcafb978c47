D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libwhatsmeow_types-7387ff75f3a99ebf.rmeta: whatsmeow-types\src\lib.rs whatsmeow-types\src\device.rs whatsmeow-types\src\error.rs whatsmeow-types\src\jid.rs whatsmeow-types\src\message.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\whatsmeow_types-7387ff75f3a99ebf.d: whatsmeow-types\src\lib.rs whatsmeow-types\src\device.rs whatsmeow-types\src\error.rs whatsmeow-types\src\jid.rs whatsmeow-types\src\message.rs Cargo.toml

whatsmeow-types\src\lib.rs:
whatsmeow-types\src\device.rs:
whatsmeow-types\src\error.rs:
whatsmeow-types\src\jid.rs:
whatsmeow-types\src\message.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
