{"$message_type":"diagnostic","message":"unused import: `Event`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":111,"byte_end":116,"line_start":4,"line_end":4,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use crate::events::{Event, EventHandler};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":111,"byte_end":118,"line_start":4,"line_end":4,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"use crate::events::{Event, EventHandler};","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":110,"byte_end":111,"line_start":4,"line_end":4,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::events::{Event, EventHandler};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":130,"byte_end":131,"line_start":4,"line_end":4,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::events::{Event, EventHandler};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Event`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\client.rs:4:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::events::{Event, EventHandler};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\lib.rs","byte_start":754,"byte_end":762,"line_start":30,"line_end":30,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\lib.rs","byte_start":750,"byte_end":763,"line_start":30,"line_end":30,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `super::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\lib.rs:30:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":268,"byte_end":272,"line_start":13,"line_end":13,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    pub async fn create_group(&self, name: &str, participants: Vec<Jid>) -> Result<Jid> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":268,"byte_end":272,"line_start":13,"line_end":13,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    pub async fn create_group(&self, name: &str, participants: Vec<Jid>) -> Result<Jid> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\group.rs:13:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn create_group(&self, name: &str, participants: Vec<Jid>) -> Result<Jid> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `participants`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":280,"byte_end":292,"line_start":13,"line_end":13,"column_start":50,"column_end":62,"is_primary":true,"text":[{"text":"    pub async fn create_group(&self, name: &str, participants: Vec<Jid>) -> Result<Jid> {","highlight_start":50,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":280,"byte_end":292,"line_start":13,"line_end":13,"column_start":50,"column_end":62,"is_primary":true,"text":[{"text":"    pub async fn create_group(&self, name: &str, participants: Vec<Jid>) -> Result<Jid> {","highlight_start":50,"highlight_end":62}],"label":null,"suggested_replacement":"_participants","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `participants`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\group.rs:13:50\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn create_group(&self, name: &str, participants: Vec<Jid>) -> Result<Jid> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_participants`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `group`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":493,"byte_end":498,"line_start":19,"line_end":19,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"    pub async fn add_participants(&self, group: Jid, participants: Vec<Jid>) -> Result<()> {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":493,"byte_end":498,"line_start":19,"line_end":19,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"    pub async fn add_participants(&self, group: Jid, participants: Vec<Jid>) -> Result<()> {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":"_group","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `group`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\group.rs:19:42\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn add_participants(&self, group: Jid, participants: Vec<Jid>) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_group`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `participants`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":505,"byte_end":517,"line_start":19,"line_end":19,"column_start":54,"column_end":66,"is_primary":true,"text":[{"text":"    pub async fn add_participants(&self, group: Jid, participants: Vec<Jid>) -> Result<()> {","highlight_start":54,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\group.rs","byte_start":505,"byte_end":517,"line_start":19,"line_end":19,"column_start":54,"column_end":66,"is_primary":true,"text":[{"text":"    pub async fn add_participants(&self, group: Jid, participants: Vec<Jid>) -> Result<()> {","highlight_start":54,"highlight_end":66}],"label":null,"suggested_replacement":"_participants","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `participants`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\group.rs:19:54\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn add_participants(&self, group: Jid, participants: Vec<Jid>) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_participants`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `data`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\media.rs","byte_start":271,"byte_end":275,"line_start":12,"line_end":12,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    pub async fn upload_media(&self, data: Vec<u8>, media_type: MediaType) -> Result<String> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\media.rs","byte_start":271,"byte_end":275,"line_start":12,"line_end":12,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    pub async fn upload_media(&self, data: Vec<u8>, media_type: MediaType) -> Result<String> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":"_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `data`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\media.rs:12:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn upload_media(&self, data: Vec<u8>, media_type: MediaType) -> Result<String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `media_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\media.rs","byte_start":286,"byte_end":296,"line_start":12,"line_end":12,"column_start":53,"column_end":63,"is_primary":true,"text":[{"text":"    pub async fn upload_media(&self, data: Vec<u8>, media_type: MediaType) -> Result<String> {","highlight_start":53,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\media.rs","byte_start":286,"byte_end":296,"line_start":12,"line_end":12,"column_start":53,"column_end":63,"is_primary":true,"text":[{"text":"    pub async fn upload_media(&self, data: Vec<u8>, media_type: MediaType) -> Result<String> {","highlight_start":53,"highlight_end":63}],"label":null,"suggested_replacement":"_media_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `media_type`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\media.rs:12:53\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn upload_media(&self, data: Vec<u8>, media_type: MediaType) -> Result<String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_media_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `url`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\media.rs","byte_start":506,"byte_end":509,"line_start":18,"line_end":18,"column_start":40,"column_end":43,"is_primary":true,"text":[{"text":"    pub async fn download_media(&self, url: &str) -> Result<Vec<u8>> {","highlight_start":40,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\media.rs","byte_start":506,"byte_end":509,"line_start":18,"line_end":18,"column_start":40,"column_end":43,"is_primary":true,"text":[{"text":"    pub async fn download_media(&self, url: &str) -> Result<Vec<u8>> {","highlight_start":40,"highlight_end":43}],"label":null,"suggested_replacement":"_url","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `url`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\media.rs:18:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn download_media(&self, url: &str) -> Result<Vec<u8>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_url`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":294,"byte_end":296,"line_start":13,"line_end":13,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":294,"byte_end":296,"line_start":13,"line_end":13,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `to`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\message.rs:13:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `text`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":303,"byte_end":307,"line_start":13,"line_end":13,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":303,"byte_end":307,"line_start":13,"line_end":13,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":"_text","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `text`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\message.rs:13:44\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_text`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":501,"byte_end":503,"line_start":19,"line_end":19,"column_start":36,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {","highlight_start":36,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":501,"byte_end":503,"line_start":19,"line_end":19,"column_start":36,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {","highlight_start":36,"highlight_end":38}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `to`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\message.rs:19:36\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `image`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":510,"byte_end":515,"line_start":19,"line_end":19,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\message.rs","byte_start":510,"byte_end":515,"line_start":19,"line_end":19,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_image","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `image`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\message.rs:19:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_image`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\presence.rs","byte_start":304,"byte_end":306,"line_start":13,"line_end":13,"column_start":37,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn send_typing(&self, to: Jid) -> Result<()> {","highlight_start":37,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\presence.rs","byte_start":304,"byte_end":306,"line_start":13,"line_end":13,"column_start":37,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn send_typing(&self, to: Jid) -> Result<()> {","highlight_start":37,"highlight_end":39}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `to`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\presence.rs:13:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_typing(&self, to: Jid) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `presence`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\presence.rs","byte_start":495,"byte_end":503,"line_start":19,"line_end":19,"column_start":39,"column_end":47,"is_primary":true,"text":[{"text":"    pub async fn send_presence(&self, presence: PresenceType) -> Result<()> {","highlight_start":39,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\presence.rs","byte_start":495,"byte_end":503,"line_start":19,"line_end":19,"column_start":39,"column_end":47,"is_primary":true,"text":[{"text":"    pub async fn send_presence(&self, presence: PresenceType) -> Result<()> {","highlight_start":39,"highlight_end":47}],"label":null,"suggested_replacement":"_presence","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `presence`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\presence.rs:19:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_presence(&self, presence: PresenceType) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_presence`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":325,"byte_end":327,"line_start":13,"line_end":13,"column_start":47,"column_end":49,"is_primary":true,"text":[{"text":"    pub async fn send_delivery_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":47,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":325,"byte_end":327,"line_start":13,"line_end":13,"column_start":47,"column_end":49,"is_primary":true,"text":[{"text":"    pub async fn send_delivery_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":47,"highlight_end":49}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `to`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\receipt.rs:13:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_delivery_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `message_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":334,"byte_end":344,"line_start":13,"line_end":13,"column_start":56,"column_end":66,"is_primary":true,"text":[{"text":"    pub async fn send_delivery_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":56,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":334,"byte_end":344,"line_start":13,"line_end":13,"column_start":56,"column_end":66,"is_primary":true,"text":[{"text":"    pub async fn send_delivery_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":56,"highlight_end":66}],"label":null,"suggested_replacement":"_message_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `message_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\receipt.rs:13:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_delivery_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_message_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":547,"byte_end":549,"line_start":19,"line_end":19,"column_start":43,"column_end":45,"is_primary":true,"text":[{"text":"    pub async fn send_read_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":43,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":547,"byte_end":549,"line_start":19,"line_end":19,"column_start":43,"column_end":45,"is_primary":true,"text":[{"text":"    pub async fn send_read_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":43,"highlight_end":45}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `to`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\receipt.rs:19:43\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_read_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `message_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":556,"byte_end":566,"line_start":19,"line_end":19,"column_start":52,"column_end":62,"is_primary":true,"text":[{"text":"    pub async fn send_read_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":52,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"whatsmeow-rs\\src\\receipt.rs","byte_start":556,"byte_end":566,"line_start":19,"line_end":19,"column_start":52,"column_end":62,"is_primary":true,"text":[{"text":"    pub async fn send_read_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {","highlight_start":52,"highlight_end":62}],"label":null,"suggested_replacement":"_message_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `message_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\receipt.rs:19:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_read_receipt(&self, to: Jid, message_id: MessageId) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_message_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `store` and `config` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":287,"byte_end":293,"line_start":11,"line_end":11,"column_start":12,"column_end":18,"is_primary":false,"text":[{"text":"pub struct Client {","highlight_start":12,"highlight_end":18}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":300,"byte_end":305,"line_start":12,"line_end":12,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    store: Arc<dyn DeviceStore>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-rs\\src\\client.rs","byte_start":394,"byte_end":400,"line_start":14,"line_end":14,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: ClientConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `store` and `config` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-rs\\src\\client.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Client {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    store: Arc<dyn DeviceStore>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    event_handlers: Arc<RwLock<Vec<Box<dyn EventHandler>>>>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: ClientConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"20 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 20 warnings emitted\u001b[0m\n\n"}
