{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"fast\", \"std\", \"zeroize\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"default\", \"digest\", \"fast\", \"hazmat\", \"legacy_compatibility\", \"merlin\", \"pem\", \"pkcs8\", \"rand_core\", \"serde\", \"signature\", \"std\", \"zeroize\"]", "target": 14975934594160758548, "profile": 2241668132362809309, "path": 14272404118380507712, "deps": [[6528079939221783635, "zeroize", false, 10736665073443638483], [9857275760291862238, "sha2", false, 9475887218659264919], [13595581133353633439, "curve25519_dalek", false, 15741216017628557007], [14313198213031843936, "ed25519", false, 2365074162511849054], [17003143334332120809, "subtle", false, 5591828839113347267]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ed25519-dalek-ba84a58dde5af46b\\dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}