D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libwhatsmeow_store-7467daff5f6296f4.rmeta: whatsmeow-store\src\lib.rs whatsmeow-store\src\error.rs whatsmeow-store\src\file.rs whatsmeow-store\src\memory.rs whatsmeow-store\src\traits.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\whatsmeow_store-7467daff5f6296f4.d: whatsmeow-store\src\lib.rs whatsmeow-store\src\error.rs whatsmeow-store\src\file.rs whatsmeow-store\src\memory.rs whatsmeow-store\src\traits.rs Cargo.toml

whatsmeow-store\src\lib.rs:
whatsmeow-store\src\error.rs:
whatsmeow-store\src\file.rs:
whatsmeow-store\src\memory.rs:
whatsmeow-store\src\traits.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
