//! Key management utilities

use crate::error::CryptoError;
use ed25519_dalek::{Signing<PERSON>ey, VerifyingKey};
use ring::rand::{SecureRandom, SystemRandom};
use x25519_dalek::{PublicKey, StaticSecret};

/// Generic key pair structure
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct KeyPair {
    pub private_key: Vec<u8>,
    pub public_key: Vec<u8>,
}

impl KeyPair {
    pub fn new(private_key: Vec<u8>, public_key: Vec<u8>) -> Self {
        Self {
            private_key,
            public_key,
        }
    }

    /// Generate a new X25519 key pair for <PERSON><PERSON><PERSON>-<PERSON><PERSON> key exchange
    pub fn generate_x25519() -> Result<Self, CryptoError> {
        let rng = SystemRandom::new();
        let mut private_bytes = [0u8; 32];
        rng.fill(&mut private_bytes)
            .map_err(|_| CryptoError::KeyGenerationFailed("Failed to generate random bytes".to_string()))?;

        let private_key = StaticSecret::from(private_bytes);
        let public_key = PublicKey::from(&private_key);

        Ok(Self {
            private_key: private_bytes.to_vec(),
            public_key: public_key.as_bytes().to_vec(),
        })
    }

    /// Generate a new Ed25519 key pair for signing
    pub fn generate_ed25519() -> Result<Self, CryptoError> {
        let rng = SystemRandom::new();
        let mut seed = [0u8; 32];
        rng.fill(&mut seed)
            .map_err(|_| CryptoError::KeyGenerationFailed("Failed to generate random seed".to_string()))?;

        let signing_key = SigningKey::from_bytes(&seed);
        let verifying_key = signing_key.verifying_key();

        Ok(Self {
            private_key: signing_key.to_bytes().to_vec(),
            public_key: verifying_key.to_bytes().to_vec(),
        })
    }

    /// Generate a new key pair (defaults to X25519 for compatibility)
    pub fn generate() -> Result<Self, CryptoError> {
        Self::generate_x25519()
    }

    /// Get the private key as X25519 StaticSecret
    pub fn as_x25519_private(&self) -> Result<StaticSecret, CryptoError> {
        if self.private_key.len() != 32 {
            return Err(CryptoError::InvalidKey("Invalid private key length for X25519".to_string()));
        }
        let mut bytes = [0u8; 32];
        bytes.copy_from_slice(&self.private_key);
        Ok(StaticSecret::from(bytes))
    }

    /// Get the public key as X25519 PublicKey
    pub fn as_x25519_public(&self) -> Result<PublicKey, CryptoError> {
        if self.public_key.len() != 32 {
            return Err(CryptoError::InvalidKey("Invalid public key length for X25519".to_string()));
        }
        let mut bytes = [0u8; 32];
        bytes.copy_from_slice(&self.public_key);
        Ok(PublicKey::from(bytes))
    }

    /// Get the private key as Ed25519 SigningKey
    pub fn as_ed25519_private(&self) -> Result<SigningKey, CryptoError> {
        if self.private_key.len() != 32 {
            return Err(CryptoError::InvalidKey("Invalid private key length for Ed25519".to_string()));
        }
        let mut bytes = [0u8; 32];
        bytes.copy_from_slice(&self.private_key);
        SigningKey::from_bytes(&bytes)
            .map_err(|e| CryptoError::InvalidKey(format!("Invalid Ed25519 private key: {}", e)))
    }

    /// Get the public key as Ed25519 VerifyingKey
    pub fn as_ed25519_public(&self) -> Result<VerifyingKey, CryptoError> {
        if self.public_key.len() != 32 {
            return Err(CryptoError::InvalidKey("Invalid public key length for Ed25519".to_string()));
        }
        let mut bytes = [0u8; 32];
        bytes.copy_from_slice(&self.public_key);
        VerifyingKey::from_bytes(&bytes)
            .map_err(|e| CryptoError::InvalidKey(format!("Invalid Ed25519 public key: {}", e)))
    }

    /// Validate the key pair consistency
    pub fn validate(&self) -> Result<(), CryptoError> {
        // Try to interpret as X25519 and validate
        if self.private_key.len() == 32 && self.public_key.len() == 32 {
            let private = self.as_x25519_private()?;
            let expected_public = PublicKey::from(&private);
            let actual_public = self.as_x25519_public()?;
            
            if expected_public.as_bytes() == actual_public.as_bytes() {
                return Ok(());
            }
        }

        // Try to interpret as Ed25519 and validate
        if self.private_key.len() == 32 && self.public_key.len() == 32 {
            let private = self.as_ed25519_private()?;
            let expected_public = private.verifying_key();
            let actual_public = self.as_ed25519_public()?;
            
            if expected_public.as_bytes() == actual_public.as_bytes() {
                return Ok(());
            }
        }

        Err(CryptoError::InvalidKey("Key pair validation failed".to_string()))
    }
}

/// Identity key wrapper for Ed25519 public keys
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct IdentityKey(pub Vec<u8>);

impl IdentityKey {
    pub fn new(key: Vec<u8>) -> Result<Self, CryptoError> {
        if key.len() != 32 {
            return Err(CryptoError::InvalidKey("Identity key must be 32 bytes".to_string()));
        }
        Ok(Self(key))
    }

    /// Create from Ed25519 VerifyingKey
    pub fn from_ed25519(key: &VerifyingKey) -> Self {
        Self(key.to_bytes().to_vec())
    }

    /// Create from KeyPair (uses public key)
    pub fn from_keypair(keypair: &KeyPair) -> Result<Self, CryptoError> {
        Self::new(keypair.public_key.clone())
    }

    /// Generate a new identity key pair
    pub fn generate() -> Result<(Self, KeyPair), CryptoError> {
        let keypair = KeyPair::generate_ed25519()?;
        let identity_key = Self::from_keypair(&keypair)?;
        Ok((identity_key, keypair))
    }

    pub fn as_bytes(&self) -> &[u8] {
        &self.0
    }

    /// Get as Ed25519 VerifyingKey
    pub fn as_ed25519(&self) -> Result<VerifyingKey, CryptoError> {
        let mut bytes = [0u8; 32];
        bytes.copy_from_slice(&self.0);
        VerifyingKey::from_bytes(&bytes)
            .map_err(|e| CryptoError::InvalidKey(format!("Invalid identity key: {}", e)))
    }

    /// Verify a signature using this identity key
    pub fn verify_signature(&self, message: &[u8], signature: &[u8]) -> Result<(), CryptoError> {
        use ed25519_dalek::{Signature, Verifier};
        
        let verifying_key = self.as_ed25519()?;
        let signature = Signature::from_slice(signature)
            .map_err(|e| CryptoError::SignatureVerificationFailed)?;
        
        verifying_key.verify(message, &signature)
            .map_err(|_| CryptoError::SignatureVerificationFailed)
    }
}

/// PreKey structure for X25519 key exchange
#[derive(Debug, Clone)]
pub struct PreKey {
    pub id: u32,
    pub key_pair: KeyPair,
    pub uploaded: bool,
    pub created_at: std::time::SystemTime,
}

impl PreKey {
    pub fn new(id: u32, key_pair: KeyPair) -> Self {
        Self { 
            id, 
            key_pair,
            uploaded: false,
            created_at: std::time::SystemTime::now(),
        }
    }

    /// Generate a new PreKey with the given ID
    pub fn generate(id: u32) -> Result<Self, CryptoError> {
        let key_pair = KeyPair::generate_x25519()?;
        Ok(Self::new(id, key_pair))
    }

    /// Mark this prekey as uploaded
    pub fn mark_uploaded(&mut self) {
        self.uploaded = true;
    }

    /// Check if this prekey has been uploaded
    pub fn is_uploaded(&self) -> bool {
        self.uploaded
    }

    /// Get the age of this prekey
    pub fn age(&self) -> std::time::Duration {
        self.created_at.elapsed().unwrap_or_default()
    }

    /// Validate the prekey
    pub fn validate(&self) -> Result<(), CryptoError> {
        self.key_pair.validate()
    }
}

/// Signed PreKey structure
#[derive(Debug, Clone)]
pub struct SignedPreKey {
    pub id: u32,
    pub key_pair: KeyPair,
    pub signature: Vec<u8>,
    pub timestamp: u64,
    pub created_at: std::time::SystemTime,
}

impl SignedPreKey {
    pub fn new(id: u32, key_pair: KeyPair, signature: Vec<u8>, timestamp: u64) -> Self {
        Self {
            id,
            key_pair,
            signature,
            timestamp,
            created_at: std::time::SystemTime::now(),
        }
    }

    /// Generate a new signed prekey
    pub fn generate(id: u32, identity_keypair: &KeyPair) -> Result<Self, CryptoError> {
        let key_pair = KeyPair::generate_x25519()?;
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        // Create signature data: key_pair.public_key
        let signature = Self::sign_prekey(&key_pair.public_key, identity_keypair)?;

        Ok(Self {
            id,
            key_pair,
            signature,
            timestamp,
            created_at: std::time::SystemTime::now(),
        })
    }

    /// Sign a prekey public key with an identity key
    fn sign_prekey(prekey_public: &[u8], identity_keypair: &KeyPair) -> Result<Vec<u8>, CryptoError> {
        use ed25519_dalek::Signer;
        
        let signing_key = identity_keypair.as_ed25519_private()?;
        let signature = signing_key.sign(prekey_public);
        Ok(signature.to_bytes().to_vec())
    }

    /// Verify the signature of this signed prekey
    pub fn verify_signature(&self, identity_key: &IdentityKey) -> Result<(), CryptoError> {
        identity_key.verify_signature(&self.key_pair.public_key, &self.signature)
    }

    /// Get the age of this signed prekey
    pub fn age(&self) -> std::time::Duration {
        self.created_at.elapsed().unwrap_or_default()
    }

    /// Check if this signed prekey is expired (older than 30 days)
    pub fn is_expired(&self) -> bool {
        self.age() > std::time::Duration::from_secs(30 * 24 * 60 * 60)
    }

    /// Validate the signed prekey
    pub fn validate(&self, identity_key: &IdentityKey) -> Result<(), CryptoError> {
        self.key_pair.validate()?;
        self.verify_signature(identity_key)?;
        Ok(())
    }
}

/// Key derivation utilities
pub struct KeyDerivation;

impl KeyDerivation {
    /// Derive a key using HKDF-SHA256
    pub fn hkdf_sha256(
        salt: &[u8],
        input_key_material: &[u8],
        info: &[u8],
        output_length: usize,
    ) -> Result<Vec<u8>, CryptoError> {
        use hkdf::Hkdf;
        use ring::digest;

        let hk = Hkdf::<ring::hmac::Context>::new(Some(salt), input_key_material);
        let mut output = vec![0u8; output_length];
        hk.expand(info, &mut output)
            .map_err(|_| CryptoError::KeyGenerationFailed("HKDF expansion failed".to_string()))?;
        
        Ok(output)
    }

    /// Perform X25519 Diffie-Hellman key exchange
    pub fn x25519_dh(private_key: &KeyPair, public_key: &[u8]) -> Result<Vec<u8>, CryptoError> {
        let private = private_key.as_x25519_private()?;
        
        if public_key.len() != 32 {
            return Err(CryptoError::InvalidKey("Public key must be 32 bytes".to_string()));
        }
        
        let mut public_bytes = [0u8; 32];
        public_bytes.copy_from_slice(public_key);
        let public = PublicKey::from(public_bytes);
        
        let shared_secret = private.diffie_hellman(&public);
        Ok(shared_secret.as_bytes().to_vec())
    }

    /// Generate a random 32-byte key
    pub fn generate_random_key() -> Result<Vec<u8>, CryptoError> {
        let rng = SystemRandom::new();
        let mut key = vec![0u8; 32];
        rng.fill(&mut key)
            .map_err(|_| CryptoError::KeyGenerationFailed("Failed to generate random key".to_string()))?;
        Ok(key)
    }

    /// Generate multiple prekeys
    pub fn generate_prekeys(start_id: u32, count: u32) -> Result<Vec<PreKey>, CryptoError> {
        let mut prekeys = Vec::with_capacity(count as usize);
        
        for i in 0..count {
            let prekey = PreKey::generate(start_id + i)?;
            prekeys.push(prekey);
        }
        
        Ok(prekeys)
    }
}

/// Key validation utilities
pub struct KeyValidator;

impl KeyValidator {
    /// Validate that a key has the correct length for its type
    pub fn validate_key_length(key: &[u8], expected_length: usize, key_type: &str) -> Result<(), CryptoError> {
        if key.len() != expected_length {
            return Err(CryptoError::InvalidKey(
                format!("{} key must be {} bytes, got {}", key_type, expected_length, key.len())
            ));
        }
        Ok(())
    }

    /// Validate an X25519 public key (check it's not all zeros or other invalid values)
    pub fn validate_x25519_public_key(key: &[u8]) -> Result<(), CryptoError> {
        Self::validate_key_length(key, 32, "X25519 public")?;
        
        // Check for all-zero key (invalid)
        if key.iter().all(|&b| b == 0) {
            return Err(CryptoError::InvalidKey("X25519 public key cannot be all zeros".to_string()));
        }
        
        // Check for all-one key (invalid)
        if key.iter().all(|&b| b == 0xFF) {
            return Err(CryptoError::InvalidKey("X25519 public key cannot be all ones".to_string()));
        }
        
        Ok(())
    }

    /// Validate an Ed25519 public key
    pub fn validate_ed25519_public_key(key: &[u8]) -> Result<(), CryptoError> {
        Self::validate_key_length(key, 32, "Ed25519 public")?;
        
        // Try to parse as Ed25519 public key
        let mut bytes = [0u8; 32];
        bytes.copy_from_slice(key);
        VerifyingKey::from_bytes(&bytes)
            .map_err(|e| CryptoError::InvalidKey(format!("Invalid Ed25519 public key: {}", e)))?;
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_keypair_generation() {
        let keypair = KeyPair::generate().unwrap();
        assert_eq!(keypair.private_key.len(), 32);
        assert_eq!(keypair.public_key.len(), 32);
        keypair.validate().unwrap();
    }

    #[test]
    fn test_x25519_keypair_generation() {
        let keypair = KeyPair::generate_x25519().unwrap();
        assert_eq!(keypair.private_key.len(), 32);
        assert_eq!(keypair.public_key.len(), 32);
        keypair.validate().unwrap();
    }

    #[test]
    fn test_ed25519_keypair_generation() {
        let keypair = KeyPair::generate_ed25519().unwrap();
        assert_eq!(keypair.private_key.len(), 32);
        assert_eq!(keypair.public_key.len(), 32);
        keypair.validate().unwrap();
    }

    #[test]
    fn test_identity_key_generation() {
        let (identity_key, keypair) = IdentityKey::generate().unwrap();
        assert_eq!(identity_key.as_bytes().len(), 32);
        
        // Verify the identity key matches the keypair public key
        assert_eq!(identity_key.as_bytes(), &keypair.public_key);
    }

    #[test]
    fn test_prekey_generation() {
        let prekey = PreKey::generate(1).unwrap();
        assert_eq!(prekey.id, 1);
        assert!(!prekey.is_uploaded());
        prekey.validate().unwrap();
    }

    #[test]
    fn test_signed_prekey_generation() {
        let identity_keypair = KeyPair::generate_ed25519().unwrap();
        let signed_prekey = SignedPreKey::generate(1, &identity_keypair).unwrap();
        
        assert_eq!(signed_prekey.id, 1);
        assert!(!signed_prekey.is_expired());
        
        let identity_key = IdentityKey::from_keypair(&identity_keypair).unwrap();
        signed_prekey.validate(&identity_key).unwrap();
    }

    #[test]
    fn test_signature_verification() {
        let identity_keypair = KeyPair::generate_ed25519().unwrap();
        let identity_key = IdentityKey::from_keypair(&identity_keypair).unwrap();
        
        let message = b"test message";
        use ed25519_dalek::Signer;
        let signing_key = identity_keypair.as_ed25519_private().unwrap();
        let signature = signing_key.sign(message);
        
        identity_key.verify_signature(message, &signature.to_bytes()).unwrap();
    }

    #[test]
    fn test_key_derivation() {
        let salt = b"test salt";
        let ikm = b"input key material";
        let info = b"test info";
        
        let derived = KeyDerivation::hkdf_sha256(salt, ikm, info, 32).unwrap();
        assert_eq!(derived.len(), 32);
        
        // Same inputs should produce same output
        let derived2 = KeyDerivation::hkdf_sha256(salt, ikm, info, 32).unwrap();
        assert_eq!(derived, derived2);
    }

    #[test]
    fn test_x25519_dh() {
        let alice_keypair = KeyPair::generate_x25519().unwrap();
        let bob_keypair = KeyPair::generate_x25519().unwrap();
        
        let alice_shared = KeyDerivation::x25519_dh(&alice_keypair, &bob_keypair.public_key).unwrap();
        let bob_shared = KeyDerivation::x25519_dh(&bob_keypair, &alice_keypair.public_key).unwrap();
        
        assert_eq!(alice_shared, bob_shared);
        assert_eq!(alice_shared.len(), 32);
    }

    #[test]
    fn test_multiple_prekey_generation() {
        let prekeys = KeyDerivation::generate_prekeys(100, 5).unwrap();
        assert_eq!(prekeys.len(), 5);
        
        for (i, prekey) in prekeys.iter().enumerate() {
            assert_eq!(prekey.id, 100 + i as u32);
            prekey.validate().unwrap();
        }
    }

    #[test]
    fn test_key_validation() {
        // Test valid key lengths
        let valid_key = vec![1u8; 32];
        KeyValidator::validate_key_length(&valid_key, 32, "test").unwrap();
        
        // Test invalid key length
        let invalid_key = vec![1u8; 31];
        assert!(KeyValidator::validate_key_length(&invalid_key, 32, "test").is_err());
        
        // Test invalid X25519 keys
        let zero_key = vec![0u8; 32];
        assert!(KeyValidator::validate_x25519_public_key(&zero_key).is_err());
        
        let ones_key = vec![0xFFu8; 32];
        assert!(KeyValidator::validate_x25519_public_key(&ones_key).is_err());
    }
}