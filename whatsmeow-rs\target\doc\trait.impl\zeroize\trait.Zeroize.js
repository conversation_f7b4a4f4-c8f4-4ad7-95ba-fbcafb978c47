(function() {
    var implementors = Object.fromEntries([["x25519_dalek",[["impl Zeroize for <a class=\"struct\" href=\"x25519_dalek/struct.EphemeralSecret.html\" title=\"struct x25519_dalek::EphemeralSecret\">EphemeralSecret</a>"],["impl Zeroize for <a class=\"struct\" href=\"x25519_dalek/struct.PublicKey.html\" title=\"struct x25519_dalek::PublicKey\">PublicKey</a>"],["impl Zeroize for <a class=\"struct\" href=\"x25519_dalek/struct.SharedSecret.html\" title=\"struct x25519_dalek::SharedSecret\">SharedSecret</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[465]}