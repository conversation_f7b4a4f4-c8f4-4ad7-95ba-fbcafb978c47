{"$message_type":"diagnostic","message":"not all trait items implemented, missing: `get_sessions_for_phone`, `get_session_count`, `clear_all_sessions`","code":{"code":"E0046","explanation":"Items are missing in a trait implementation.\n\nErroneous code example:\n\n```compile_fail,E0046\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {}\n// error: not all trait items implemented, missing: `foo`\n```\n\nWhen trying to make some type implement a trait `Foo`, you must, at minimum,\nprovide implementations for all of `Foo`'s required methods (meaning the\nmethods that do not have default implementations), as well as any required\ntrait items like associated types or constants. Example:\n\n```\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn foo() {} // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":1330,"byte_end":1363,"line_start":50,"line_end":50,"column_start":1,"column_end":34,"is_primary":true,"text":[{"text":"impl SessionStore for MemoryStore {","highlight_start":1,"highlight_end":34}],"label":"missing `get_sessions_for_phone`, `get_session_count`, `clear_all_sessions` in implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":1583,"byte_end":1683,"line_start":47,"line_end":47,"column_start":5,"column_end":105,"is_primary":false,"text":[{"text":"    async fn get_sessions_for_phone(&self, phone: &str) -> Result<HashMap<String, Vec<u8>>, StoreError>;","highlight_start":5,"highlight_end":105}],"label":"`get_sessions_for_phone` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":1904,"byte_end":1967,"line_start":55,"line_end":55,"column_start":5,"column_end":68,"is_primary":false,"text":[{"text":"    async fn get_session_count(&self) -> Result<usize, StoreError>;","highlight_start":5,"highlight_end":68}],"label":"`get_session_count` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":2004,"byte_end":2065,"line_start":58,"line_end":58,"column_start":5,"column_end":66,"is_primary":false,"text":[{"text":"    async fn clear_all_sessions(&self) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":66}],"label":"`clear_all_sessions` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"implement the missing item: `fn get_sessions_for_phone(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<HashMap<String, Vec<u8>>, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":2195,"byte_end":2195,"line_start":73,"line_end":73,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_sessions_for_phone(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<HashMap<String, Vec<u8>>, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn get_session_count(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<usize, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":2195,"byte_end":2195,"line_start":73,"line_end":73,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_session_count(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<usize, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn clear_all_sessions(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":2195,"byte_end":2195,"line_start":73,"line_end":73,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn clear_all_sessions(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0046]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: not all trait items implemented, missing: `get_sessions_for_phone`, `get_session_count`, `clear_all_sessions`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\memory.rs:50:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SessionStore for MemoryStore {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `get_sessions_for_phone`, `get_session_count`, `clear_all_sessions` in implementation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mwhatsmeow-store\\src\\traits.rs:47:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_sessions_for_phone(&self, phone: &str) -> Result<HashMap<String, Vec<u8>>, StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_sessions_for_phone` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_session_count(&self) -> Result<usize, StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_session_count` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn clear_all_sessions(&self) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`clear_all_sessions` from trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"not all trait items implemented, missing: `get_all_prekeys`, `get_unuploaded_prekeys`, `get_prekey_count`, `remove_prekeys`, `clear_all_prekeys`, `get_next_prekey_id`","code":{"code":"E0046","explanation":"Items are missing in a trait implementation.\n\nErroneous code example:\n\n```compile_fail,E0046\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {}\n// error: not all trait items implemented, missing: `foo`\n```\n\nWhen trying to make some type implement a trait `Foo`, you must, at minimum,\nprovide implementations for all of `Foo`'s required methods (meaning the\nmethods that do not have default implementations), as well as any required\ntrait items like associated types or constants. Example:\n\n```\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn foo() {} // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":2213,"byte_end":2245,"line_start":76,"line_end":76,"column_start":1,"column_end":33,"is_primary":true,"text":[{"text":"impl PreKeyStore for MemoryStore {","highlight_start":1,"highlight_end":33}],"label":"missing `get_all_prekeys`, `get_unuploaded_prekeys`, `get_prekey_count`, `remove_prekeys`, `clear_all_prekeys`, `get_next_prekey_id` in implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":2741,"byte_end":2808,"line_start":77,"line_end":77,"column_start":5,"column_end":72,"is_primary":false,"text":[{"text":"    async fn get_all_prekeys(&self) -> Result<Vec<PreKey>, StoreError>;","highlight_start":5,"highlight_end":72}],"label":"`get_all_prekeys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":2849,"byte_end":2923,"line_start":80,"line_end":80,"column_start":5,"column_end":79,"is_primary":false,"text":[{"text":"    async fn get_unuploaded_prekeys(&self) -> Result<Vec<PreKey>, StoreError>;","highlight_start":5,"highlight_end":79}],"label":"`get_unuploaded_prekeys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":2976,"byte_end":3038,"line_start":83,"line_end":83,"column_start":5,"column_end":67,"is_primary":false,"text":[{"text":"    async fn get_prekey_count(&self) -> Result<usize, StoreError>;","highlight_start":5,"highlight_end":67}],"label":"`get_prekey_count` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":3087,"byte_end":3157,"line_start":86,"line_end":86,"column_start":5,"column_end":75,"is_primary":false,"text":[{"text":"    async fn remove_prekeys(&self, ids: &[u32]) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":75}],"label":"`remove_prekeys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":3193,"byte_end":3253,"line_start":89,"line_end":89,"column_start":5,"column_end":65,"is_primary":false,"text":[{"text":"    async fn clear_all_prekeys(&self) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":65}],"label":"`clear_all_prekeys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":3304,"byte_end":3366,"line_start":92,"line_end":92,"column_start":5,"column_end":67,"is_primary":false,"text":[{"text":"    async fn get_next_prekey_id(&self) -> Result<u32, StoreError>;","highlight_start":5,"highlight_end":67}],"label":"`get_next_prekey_id` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"implement the missing item: `fn get_all_prekeys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<Vec<PreKey>, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3434,"byte_end":3434,"line_start":113,"line_end":113,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_all_prekeys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<Vec<PreKey>, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn get_unuploaded_prekeys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<Vec<PreKey>, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3434,"byte_end":3434,"line_start":113,"line_end":113,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_unuploaded_prekeys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<Vec<PreKey>, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn get_prekey_count(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<usize, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3434,"byte_end":3434,"line_start":113,"line_end":113,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_prekey_count(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<usize, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn remove_prekeys(&'life0 self, _: &'life1 [u32]) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3434,"byte_end":3434,"line_start":113,"line_end":113,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn remove_prekeys(&'life0 self, _: &'life1 [u32]) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn clear_all_prekeys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3434,"byte_end":3434,"line_start":113,"line_end":113,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn clear_all_prekeys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn get_next_prekey_id(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<u32, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3434,"byte_end":3434,"line_start":113,"line_end":113,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_next_prekey_id(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<u32, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0046]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: not all trait items implemented, missing: `get_all_prekeys`, `get_unuploaded_prekeys`, `get_prekey_count`, `remove_prekeys`, `clear_all_prekeys`, `get_next_prekey_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\memory.rs:76:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl PreKeyStore for MemoryStore {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `get_all_prekeys`, `get_unuploaded_prekeys`, `get_prekey_count`, `remove_prekeys`, `clear_all_prekeys`, `get_next_prekey_id` in implementation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mwhatsmeow-store\\src\\traits.rs:77:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_all_prekeys(&self) -> Result<Vec<PreKey>, StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_all_prekeys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_unuploaded_prekeys(&self) -> Result<Vec<PreKey>, StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_unuploaded_prekeys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m83\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_prekey_count(&self) -> Result<usize, StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_prekey_count` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn remove_prekeys(&self, ids: &[u32]) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`remove_prekeys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn clear_all_prekeys(&self) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`clear_all_prekeys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_next_prekey_id(&self) -> Result<u32, StoreError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_next_prekey_id` from trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"not all trait items implemented, missing: `delete_sender_key`, `get_group_sender_keys`, `delete_group_sender_keys`, `clear_all_sender_keys`","code":{"code":"E0046","explanation":"Items are missing in a trait implementation.\n\nErroneous code example:\n\n```compile_fail,E0046\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {}\n// error: not all trait items implemented, missing: `foo`\n```\n\nWhen trying to make some type implement a trait `Foo`, you must, at minimum,\nprovide implementations for all of `Foo`'s required methods (meaning the\nmethods that do not have default implementations), as well as any required\ntrait items like associated types or constants. Example:\n\n```\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn foo() {} // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":3452,"byte_end":3487,"line_start":116,"line_end":116,"column_start":1,"column_end":36,"is_primary":true,"text":[{"text":"impl SenderKeyStore for MemoryStore {","highlight_start":1,"highlight_end":36}],"label":"missing `delete_sender_key`, `get_group_sender_keys`, `delete_group_sender_keys`, `clear_all_sender_keys` in implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":3962,"byte_end":4086,"line_start":114,"line_end":118,"column_start":5,"column_end":33,"is_primary":false,"text":[{"text":"    async fn delete_sender_key(","highlight_start":5,"highlight_end":32},{"text":"        &self,","highlight_start":1,"highlight_end":15},{"text":"        group_id: &str,","highlight_start":1,"highlight_end":24},{"text":"        sender_id: &str,","highlight_start":1,"highlight_end":25},{"text":"    ) -> Result<(), StoreError>;","highlight_start":1,"highlight_end":33}],"label":"`delete_sender_key` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":4136,"byte_end":4238,"line_start":121,"line_end":121,"column_start":5,"column_end":107,"is_primary":false,"text":[{"text":"    async fn get_group_sender_keys(&self, group_id: &str) -> Result<HashMap<String, Vec<u8>>, StoreError>;","highlight_start":5,"highlight_end":107}],"label":"`get_group_sender_keys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":4291,"byte_end":4374,"line_start":124,"line_end":124,"column_start":5,"column_end":88,"is_primary":false,"text":[{"text":"    async fn delete_group_sender_keys(&self, group_id: &str) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":88}],"label":"`delete_group_sender_keys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":4630,"byte_end":4694,"line_start":132,"line_end":132,"column_start":5,"column_end":69,"is_primary":false,"text":[{"text":"    async fn clear_all_sender_keys(&self) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":69}],"label":"`clear_all_sender_keys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"implement the missing item: `fn delete_sender_key(&'life0 self, _: &'life1 str, _: &'life2 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4134,"byte_end":4134,"line_start":138,"line_end":138,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn delete_sender_key(&'life0 self, _: &'life1 str, _: &'life2 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn get_group_sender_keys(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<HashMap<String, Vec<u8>>, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4134,"byte_end":4134,"line_start":138,"line_end":138,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_group_sender_keys(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<HashMap<String, Vec<u8>>, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn delete_group_sender_keys(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4134,"byte_end":4134,"line_start":138,"line_end":138,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn delete_group_sender_keys(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn clear_all_sender_keys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4134,"byte_end":4134,"line_start":138,"line_end":138,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn clear_all_sender_keys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0046]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: not all trait items implemented, missing: `delete_sender_key`, `get_group_sender_keys`, `delete_group_sender_keys`, `clear_all_sender_keys`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\memory.rs:116:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mimpl SenderKeyStore for MemoryStore {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `delete_sender_key`, `get_group_sender_keys`, `delete_group_sender_keys`, `clear_all_sender_keys` in implementation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mwhatsmeow-store\\src\\traits.rs:114:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn delete_sender_key(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        group_id: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sender_id: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|________________________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`delete_sender_key` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    async fn get_group_sender_keys(&self, group_id: &str) -> Result<HashMap<String, Vec<u8>>, StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_group_sender_keys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    async fn delete_group_sender_keys(&self, group_id: &str) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`delete_group_sender_keys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    async fn clear_all_sender_keys(&self) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`clear_all_sender_keys` from trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"not all trait items implemented, missing: `delete_identity_key`, `get_all_identity_keys`, `mark_identity_trusted`, `mark_identity_untrusted`, `clear_all_identity_keys`","code":{"code":"E0046","explanation":"Items are missing in a trait implementation.\n\nErroneous code example:\n\n```compile_fail,E0046\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {}\n// error: not all trait items implemented, missing: `foo`\n```\n\nWhen trying to make some type implement a trait `Foo`, you must, at minimum,\nprovide implementations for all of `Foo`'s required methods (meaning the\nmethods that do not have default implementations), as well as any required\ntrait items like associated types or constants. Example:\n\n```\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn foo() {} // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4152,"byte_end":4186,"line_start":141,"line_end":141,"column_start":1,"column_end":35,"is_primary":true,"text":[{"text":"impl IdentityStore for MemoryStore {","highlight_start":1,"highlight_end":35}],"label":"missing `delete_identity_key`, `get_all_identity_keys`, `mark_identity_trusted`, `mark_identity_untrusted`, `clear_all_identity_keys` in implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":5275,"byte_end":5352,"line_start":148,"line_end":148,"column_start":5,"column_end":82,"is_primary":false,"text":[{"text":"    async fn delete_identity_key(&self, address: &str) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":82}],"label":"`delete_identity_key` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":5392,"byte_end":5478,"line_start":151,"line_end":151,"column_start":5,"column_end":91,"is_primary":false,"text":[{"text":"    async fn get_all_identity_keys(&self) -> Result<HashMap<String, Vec<u8>>, StoreError>;","highlight_start":5,"highlight_end":91}],"label":"`get_all_identity_keys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":5528,"byte_end":5619,"line_start":154,"line_end":154,"column_start":5,"column_end":96,"is_primary":false,"text":[{"text":"    async fn mark_identity_trusted(&self, address: &str, key: &[u8]) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":96}],"label":"`mark_identity_trusted` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":5671,"byte_end":5752,"line_start":157,"line_end":157,"column_start":5,"column_end":86,"is_primary":false,"text":[{"text":"    async fn mark_identity_untrusted(&self, address: &str) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":86}],"label":"`mark_identity_untrusted` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"whatsmeow-store\\src\\traits.rs","byte_start":5987,"byte_end":6053,"line_start":165,"line_end":165,"column_start":5,"column_end":71,"is_primary":false,"text":[{"text":"    async fn clear_all_identity_keys(&self) -> Result<(), StoreError>;","highlight_start":5,"highlight_end":71}],"label":"`clear_all_identity_keys` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"implement the missing item: `fn delete_identity_key(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4967,"byte_end":4967,"line_start":160,"line_end":160,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn delete_identity_key(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn get_all_identity_keys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<HashMap<String, Vec<u8>>, StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4967,"byte_end":4967,"line_start":160,"line_end":160,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn get_all_identity_keys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<HashMap<String, Vec<u8>>, StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn mark_identity_trusted(&'life0 self, _: &'life1 str, _: &'life2 [u8]) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4967,"byte_end":4967,"line_start":160,"line_end":160,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn mark_identity_trusted(&'life0 self, _: &'life1 str, _: &'life2 [u8]) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn mark_identity_untrusted(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4967,"byte_end":4967,"line_start":160,"line_end":160,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn mark_identity_untrusted(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn clear_all_identity_keys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\memory.rs","byte_start":4967,"byte_end":4967,"line_start":160,"line_end":160,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn clear_all_identity_keys(&'life0 self) -> Pin<Box<(dyn Future<Output = Result<(), StoreError>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0046]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: not all trait items implemented, missing: `delete_identity_key`, `get_all_identity_keys`, `mark_identity_trusted`, `mark_identity_untrusted`, `clear_all_identity_keys`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\memory.rs:141:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl IdentityStore for MemoryStore {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `delete_identity_key`, `get_all_identity_keys`, `mark_identity_trusted`, `mark_identity_untrusted`, `clear_all_identity_keys` in implementation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mwhatsmeow-store\\src\\traits.rs:148:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn delete_identity_key(&self, address: &str) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`delete_identity_key` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_all_identity_keys(&self) -> Result<HashMap<String, Vec<u8>>, StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`get_all_identity_keys` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn mark_identity_trusted(&self, address: &str, key: &[u8]) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`mark_identity_trusted` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m157\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn mark_identity_untrusted(&self, address: &str) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`mark_identity_untrusted` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn clear_all_identity_keys(&self) -> Result<(), StoreError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`clear_all_identity_keys` from trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 4 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 4 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0046`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0046`.\u001b[0m\n"}
