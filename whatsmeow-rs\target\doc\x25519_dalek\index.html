<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="x25519-dalek    CI"><title>x25519_dalek - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="x25519_dalek" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../static.files/storage-82c7156e.js"></script><script defer src="../crates.js"></script><script defer src="../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod crate"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button><a class="logo-container" href="../x25519_dalek/index.html"><img src="https://cdn.jsdelivr.net/gh/dalek-cryptography/curve25519-dalek/docs/assets/dalek-logo-clear.png" alt=""></a></nav><nav class="sidebar"><div class="sidebar-crate"><a class="logo-container" href="../x25519_dalek/index.html"><img src="https://cdn.jsdelivr.net/gh/dalek-cryptography/curve25519-dalek/docs/assets/dalek-logo-clear.png" alt="logo"></a><h2><a href="../x25519_dalek/index.html">x25519_<wbr>dalek</a><span class="version">2.0.1</span></h2></div><div class="sidebar-elems"><ul class="block"><li><a id="all-types" href="all.html">All Items</a></li></ul><section id="rustdoc-toc"><h3><a href="#">Sections</a></h3><ul class="block top-toc"><li><a href="#x25519-dalek----ci" title="x25519-dalek    CI">x25519-dalek    CI</a><ul><li><a href="#examples" title="Examples">Examples</a></li></ul></li><li><a href="#installation" title="Installation">Installation</a></li><li><a href="#msrv" title="MSRV">MSRV</a></li><li><a href="#documentation" title="Documentation">Documentation</a></li><li><a href="#performance-and-backend-selection" title="Performance and backend selection">Performance and backend selection</a></li><li><a href="#note" title="Note">Note</a></li><li><a href="#see-also" title="See also">See also</a></li></ul><h3><a href="#structs">Crate Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#constants" title="Constants">Constants</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1>Crate <span>x25519_dalek</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/x25519_dalek/lib.rs.html#17-33">Source</a> </span></div><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><h2 id="x25519-dalek----ci"><a class="doc-anchor" href="#x25519-dalek----ci">§</a>x25519-dalek  <a href="https://crates.io/crates/x25519-dalek"><img src="https://img.shields.io/crates/v/x25519-dalek.svg" alt="" /></a> <a href="https://docs.rs/x25519-dalek"><img src="https://docs.rs/x25519-dalek/badge.svg" alt="" /></a> <a href="https://github.com/dalek-cryptography/curve25519-dalek/actions/workflows/x25519-dalek.yml"><img src="https://github.com/dalek-cryptography/curve25519-dalek/actions/workflows/x25519-dalek.yml/badge.svg?branch=main" alt="CI" /></a></h2>
<p>A pure-Rust implementation of x25519 elliptic curve Diffie-Hellman key exchange,
with curve operations provided by
<a href="https://github.com/dalek-cryptography/curve25519-dalek">curve25519-dalek</a>.</p>
<p>This crate provides two levels of API: a bare byte-oriented <code>x25519</code>
function which matches the function specified in <a href="https://tools.ietf.org/html/rfc7748">RFC7748</a>, as
well as a higher-level Rust API for static and ephemeral Diffie-Hellman.</p>
<h3 id="examples"><a class="doc-anchor" href="#examples">§</a>Examples</h3><a href="https://shop.bubblesort.io">
<img
  style="float: right; width: auto; height: 300px;"
  src="https://raw.githubusercontent.com/dalek-cryptography/x25519-dalek/master/res/bubblesort-zines-secret-messages-cover.jpeg"/>
</a>
<p>Alice and Bob are two adorable kittens who have lost their mittens, and they
wish to be able to send secret messages to each other to coordinate finding
them, otherwise—if their caretaker cat finds out—they will surely be called
naughty kittens and be given no pie!</p>
<p>But the two kittens are quite clever.  Even though their paws are still too big
and the rest of them is 90% fuzziness, these clever kittens have been studying
up on modern public key cryptography and have learned a nifty trick called
<em>elliptic curve Diffie-Hellman key exchange</em>.  With the right incantations, the
kittens will be able to secretly organise to find their mittens, and then spend
the rest of the afternoon nomming some yummy pie!</p>
<p>First, Alice uses <code>EphemeralSecret::random()</code> and then
<code>PublicKey::from()</code> to produce her secret and public keys:</p>

<div class="example-wrap ignore"><a href="#" class="tooltip" title="This example is not tested">ⓘ</a><pre class="rust rust-example-rendered"><code><span class="kw">use </span>x25519_dalek::{EphemeralSecret, PublicKey};

<span class="kw">let </span>alice_secret = EphemeralSecret::random();
<span class="kw">let </span>alice_public = PublicKey::from(<span class="kw-2">&amp;</span>alice_secret);</code></pre></div>
<p>Bob does the same:</p>

<div class="example-wrap ignore"><a href="#" class="tooltip" title="This example is not tested">ⓘ</a><pre class="rust rust-example-rendered"><code><span class="kw">let </span>bob_secret = EphemeralSecret::random();
<span class="kw">let </span>bob_public = PublicKey::from(<span class="kw-2">&amp;</span>bob_secret);</code></pre></div>
<p>Alice meows across the room, telling <code>alice_public</code> to Bob, and Bob
loudly meows <code>bob_public</code> back to Alice.  Alice now computes her
shared secret with Bob by doing:</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">let </span>alice_shared_secret = alice_secret.diffie_hellman(<span class="kw-2">&amp;</span>bob_public);</code></pre></div>
<p>Similarly, Bob computes a shared secret by doing:</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">let </span>bob_shared_secret = bob_secret.diffie_hellman(<span class="kw-2">&amp;</span>alice_public);</code></pre></div>
<p>These secrets are the same:</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="macro">assert_eq!</span>(alice_shared_secret.as_bytes(), bob_shared_secret.as_bytes());</code></pre></div>
<p>Voilà!  Alice and Bob can now use their shared secret to encrypt their
meows, for example, by using it to generate a key and nonce for an
authenticated-encryption cipher.</p>
<p>This example used the ephemeral DH API, which ensures that secret keys
cannot be reused; Alice and Bob could instead use the static DH API
and load a long-term secret key.</p>
<h2 id="installation"><a class="doc-anchor" href="#installation">§</a>Installation</h2>
<p>To install, add the following to your project’s <code>Cargo.toml</code>:</p>
<div class="example-wrap"><pre class="language-toml"><code>[dependencies]
x25519-dalek = &quot;2&quot;</code></pre></div><h2 id="msrv"><a class="doc-anchor" href="#msrv">§</a>MSRV</h2>
<p>Current MSRV is 1.60.</p>
<h2 id="documentation"><a class="doc-anchor" href="#documentation">§</a>Documentation</h2>
<p>Documentation is available <a href="https://docs.rs/x25519-dalek">here</a>.</p>
<h2 id="performance-and-backend-selection"><a class="doc-anchor" href="#performance-and-backend-selection">§</a>Performance and backend selection</h2>
<p>Performance is a secondary goal behind correctness, safety, and clarity, but we aim to be competitive with other implementations. To this end, we allow users to choose their <em>backend</em>, i.e., the underlying implementation of elliptic curve and scalar arithmetic. Different backends have different use cases. For example, if you demand formally verified code, you want to use the <code>fiat</code> backend (as it was generated from <a href="https://github.com/mit-plv/fiat-crypto">Fiat Crypto</a>).</p>
<p>Further instructions and details regarding backends can be found in the <a href="https://github.com/dalek-cryptography/curve25519-dalek#backends">curve25519-dalek docs</a>.</p>
<h2 id="note"><a class="doc-anchor" href="#note">§</a>Note</h2>
<p>This code matches the <a href="https://tools.ietf.org/html/rfc7748">RFC7748</a> test vectors.
The elliptic curve
operations are provided by <code>curve25519-dalek</code>, which makes a best-effort
attempt to prevent software side-channels.</p>
<p>“Secret Messages” cover image and <a href="https://shop.bubblesort.io/products/secret-messages-zine">zine</a>
copyright © Amy Wibowo (<a href="https://twitter.com/sailorhg">@sailorhg</a>)</p>
<h2 id="see-also"><a class="doc-anchor" href="#see-also">§</a>See also</h2>
<ul>
<li><a href="https://github.com/RustCrypto/nacl-compat/tree/master/crypto_box">crypto_box</a>: pure Rust public-key authenticated encryption compatible with
the NaCl family of encryption libraries (libsodium, TweetNaCl) which uses
<code>x25519-dalek</code> for key agreement</li>
</ul>
</div></details><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.EphemeralSecret.html" title="struct x25519_dalek::EphemeralSecret">Ephemeral<wbr>Secret</a></dt><dd>A short-lived Diffie-Hellman secret key that can only be used to compute a single
<a href="struct.SharedSecret.html" title="struct x25519_dalek::SharedSecret"><code>SharedSecret</code></a>.</dd><dt><a class="struct" href="struct.PublicKey.html" title="struct x25519_dalek::PublicKey">Public<wbr>Key</a></dt><dd>A Diffie-Hellman public key</dd><dt><a class="struct" href="struct.SharedSecret.html" title="struct x25519_dalek::SharedSecret">Shared<wbr>Secret</a></dt><dd>The result of a Diffie-Hellman key exchange.</dd></dl><h2 id="constants" class="section-header">Constants<a href="#constants" class="anchor">§</a></h2><dl class="item-table"><dt><a class="constant" href="constant.X25519_BASEPOINT_BYTES.html" title="constant x25519_dalek::X25519_BASEPOINT_BYTES">X25519_<wbr>BASEPOINT_<wbr>BYTES</a></dt><dd>The X25519 basepoint, for use with the bare, byte-oriented x25519
function.  This is provided for people who cannot use the typed
DH API for some reason.</dd></dl><h2 id="functions" class="section-header">Functions<a href="#functions" class="anchor">§</a></h2><dl class="item-table"><dt><a class="fn" href="fn.x25519.html" title="fn x25519_dalek::x25519">x25519</a></dt><dd>The bare, byte-oriented x25519 function, exactly as specified in RFC7748.</dd></dl></section></div></main></body></html>