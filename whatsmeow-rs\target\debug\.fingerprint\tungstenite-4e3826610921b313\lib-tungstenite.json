{"rustc": 16591470773350601817, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 2241668132362809309, "path": 16692170551363587158, "deps": [[99287295355353247, "data_encoding", false, 11789350342170338228], [2098583196738611028, "rand", false, 1794063863596631365], [4359956005902820838, "utf8", false, 12631267875934327747], [5986029879202738730, "log", false, 854695365147505107], [6163892036024256188, "httparse", false, 17187483101928227982], [9010263965687315507, "http", false, 15283923244554771932], [10724389056617919257, "sha1", false, 7953102566406111732], [10806645703491011684, "thiserror", false, 1551337121736502205], [16066129441945555748, "bytes", false, 18098907191387422521]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-4e3826610921b313\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}