(function() {
    var implementors = Object.fromEntries([["x25519_dalek",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"x25519_dalek/struct.EphemeralSecret.html\" title=\"struct x25519_dalek::EphemeralSecret\">EphemeralSecret</a>",1,["x25519_dalek::x25519::EphemeralSecret"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"x25519_dalek/struct.PublicKey.html\" title=\"struct x25519_dalek::PublicKey\">PublicKey</a>",1,["x25519_dalek::x25519::PublicKey"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"x25519_dalek/struct.SharedSecret.html\" title=\"struct x25519_dalek::SharedSecret\">SharedSecret</a>",1,["x25519_dalek::x25519::SharedSecret"]]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[1113]}