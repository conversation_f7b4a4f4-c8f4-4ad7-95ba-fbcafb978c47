{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2241668132362809309, "path": 10711271149035353054, "deps": [[2828590642173593838, "cfg_if", false, 7231727090772693290], [5170503507811329045, "build_script_build", false, 8984671669035733865]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-632b00c693453431\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}