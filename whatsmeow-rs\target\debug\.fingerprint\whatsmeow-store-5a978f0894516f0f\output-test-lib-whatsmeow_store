{"$message_type":"diagnostic","message":"function call inside of `expect`","code":{"code":"clippy::expect_fun_call","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-store\\src\\file.rs","byte_start":45269,"byte_end":45316,"line_start":1292,"line_end":1292,"column_start":22,"column_end":69,"is_primary":true,"text":[{"text":"                    .expect(&format!(\"Failed to put session {}\", i));","highlight_start":22,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_fun_call","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::expect_fun_call)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\file.rs","byte_start":45269,"byte_end":45316,"line_start":1292,"line_end":1292,"column_start":22,"column_end":69,"is_primary":true,"text":[{"text":"                    .expect(&format!(\"Failed to put session {}\", i));","highlight_start":22,"highlight_end":69}],"label":null,"suggested_replacement":"unwrap_or_else(|_| panic!(\"Failed to put session {}\", i))","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function call inside of `expect`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\file.rs:1292:22\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .expect(&format!(\"Failed to put session {}\", i));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `unwrap_or_else(|_| panic!(\"Failed to put session {}\", i))`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_fun_call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::expect_fun_call)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function call inside of `expect`","code":{"code":"clippy::expect_fun_call","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-store\\src\\file.rs","byte_start":45640,"byte_end":45687,"line_start":1301,"line_end":1301,"column_start":22,"column_end":69,"is_primary":true,"text":[{"text":"                    .expect(&format!(\"Failed to get session {}\", i));","highlight_start":22,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_fun_call","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\file.rs","byte_start":45640,"byte_end":45687,"line_start":1301,"line_end":1301,"column_start":22,"column_end":69,"is_primary":true,"text":[{"text":"                    .expect(&format!(\"Failed to get session {}\", i));","highlight_start":22,"highlight_end":69}],"label":null,"suggested_replacement":"unwrap_or_else(|_| panic!(\"Failed to get session {}\", i))","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function call inside of `expect`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\file.rs:1301:22\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .expect(&format!(\"Failed to get session {}\", i));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `unwrap_or_else(|_| panic!(\"Failed to get session {}\", i))`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_fun_call\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function call inside of `expect`","code":{"code":"clippy::expect_fun_call","explanation":null},"level":"warning","spans":[{"file_name":"whatsmeow-store\\src\\file.rs","byte_start":45968,"byte_end":46005,"line_start":1309,"line_end":1309,"column_start":26,"column_end":63,"is_primary":true,"text":[{"text":"            handle.await.expect(&format!(\"Task {} failed\", i));","highlight_start":26,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_fun_call","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"whatsmeow-store\\src\\file.rs","byte_start":45968,"byte_end":46005,"line_start":1309,"line_end":1309,"column_start":26,"column_end":63,"is_primary":true,"text":[{"text":"            handle.await.expect(&format!(\"Task {} failed\", i));","highlight_start":26,"highlight_end":63}],"label":null,"suggested_replacement":"unwrap_or_else(|_| panic!(\"Task {} failed\", i))","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function call inside of `expect`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mwhatsmeow-store\\src\\file.rs:1309:26\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1309\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            handle.await.expect(&format!(\"Task {} failed\", i));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `unwrap_or_else(|_| panic!(\"Task {} failed\", i))`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_fun_call\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"3 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 3 warnings emitted\u001b[0m\n\n"}
