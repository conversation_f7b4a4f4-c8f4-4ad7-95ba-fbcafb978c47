<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\x25519-dalek-2.0.1\src\lib.rs`."><title>lib.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="x25519_dalek" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">x25519_dalek/</div>lib.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="comment">// -*- mode: rust; -*-
<a href=#2 id=2 data-nosnippet>2</a>//
<a href=#3 id=3 data-nosnippet>3</a>// This file is part of x25519-dalek.
<a href=#4 id=4 data-nosnippet>4</a>// Copyright (c) 2017-2021 isis lovecruft
<a href=#5 id=5 data-nosnippet>5</a>// Copyright (c) 2019-2021 DebugSteven
<a href=#6 id=6 data-nosnippet>6</a>// See LICENSE for licensing information.
<a href=#7 id=7 data-nosnippet>7</a>//
<a href=#8 id=8 data-nosnippet>8</a>// Authors: <AUTHORS>
<a href=#10 id=10 data-nosnippet>10</a>// - DebugSteven &lt;<EMAIL>&gt;
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a>// Refuse to compile if documentation is missing, but only on nightly.
<a href=#13 id=13 data-nosnippet>13</a>//
<a href=#14 id=14 data-nosnippet>14</a>// This means that missing docs will still fail CI, but means we can use
<a href=#15 id=15 data-nosnippet>15</a>// README.md as the crate documentation.
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a></span><span class="attr">#![no_std]
<a href=#18 id=18 data-nosnippet>18</a>#![cfg_attr(feature = <span class="string">"bench"</span>, feature(test))]
<a href=#19 id=19 data-nosnippet>19</a>#![cfg_attr(docsrs, feature(doc_auto_cfg, doc_cfg, doc_cfg_hide))]
<a href=#20 id=20 data-nosnippet>20</a>#![cfg_attr(docsrs, doc(cfg_hide(docsrs)))]
<a href=#21 id=21 data-nosnippet>21</a>#![deny(missing_docs)]
<a href=#22 id=22 data-nosnippet>22</a>#![doc(
<a href=#23 id=23 data-nosnippet>23</a>    html_logo_url = <span class="string">"https://cdn.jsdelivr.net/gh/dalek-cryptography/curve25519-dalek/docs/assets/dalek-logo-clear.png"
<a href=#24 id=24 data-nosnippet>24</a></span>)]
<a href=#25 id=25 data-nosnippet>25</a>#![doc = <span class="macro">include_str!</span>(<span class="string">"../README.md"</span>)]
<a href=#26 id=26 data-nosnippet>26</a>
<a href=#27 id=27 data-nosnippet>27</a></span><span class="comment">//------------------------------------------------------------------------
<a href=#28 id=28 data-nosnippet>28</a>// x25519-dalek public API
<a href=#29 id=29 data-nosnippet>29</a>//------------------------------------------------------------------------
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a></span><span class="kw">mod </span>x25519;
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a><span class="kw">pub use </span><span class="kw">crate</span>::x25519::<span class="kw-2">*</span>;</code></pre></div></section></main></body></html>