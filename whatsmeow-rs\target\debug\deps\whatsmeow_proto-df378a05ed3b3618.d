D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\libwhatsmeow_proto-df378a05ed3b3618.rmeta: whatsmeow-proto\src\lib.rs whatsmeow-proto\src\conversion\mod.rs whatsmeow-proto\src\error.rs whatsmeow-proto\src\utils.rs D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\build\whatsmeow-proto-5888004f95d45925\out/wa_proto.rs Cargo.toml

D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\deps\whatsmeow_proto-df378a05ed3b3618.d: whatsmeow-proto\src\lib.rs whatsmeow-proto\src\conversion\mod.rs whatsmeow-proto\src\error.rs whatsmeow-proto\src\utils.rs D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\build\whatsmeow-proto-5888004f95d45925\out/wa_proto.rs Cargo.toml

whatsmeow-proto\src\lib.rs:
whatsmeow-proto\src\conversion\mod.rs:
whatsmeow-proto\src\error.rs:
whatsmeow-proto\src\utils.rs:
D:\programming\Rust\whatsapp-rs-clone\whatsmeow-rs\target\debug\build\whatsmeow-proto-5888004f95d45925\out/wa_proto.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
# env-dep:OUT_DIR=D:\\programming\\Rust\\whatsapp-rs-clone\\whatsmeow-rs\\target\\debug\\build\\whatsmeow-proto-5888004f95d45925\\out
