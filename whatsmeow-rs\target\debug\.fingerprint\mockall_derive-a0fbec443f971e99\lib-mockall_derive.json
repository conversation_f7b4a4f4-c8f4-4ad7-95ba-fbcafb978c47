{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"nightly_derive\"]", "target": 18170435632028880183, "profile": 2225463790103693989, "path": 15340792335132861312, "deps": [[2828590642173593838, "cfg_if", false, 9843122439619667624], [3060637413840920116, "proc_macro2", false, 13833104526989948624], [4974441333307933176, "syn", false, 16690932296537350863], [9001202093189684693, "build_script_build", false, 14435808289100450434], [17990358020177143287, "quote", false, 7153210784266032170]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\mockall_derive-a0fbec443f971e99\\dep-lib-mockall_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}