{"rustc": 16591470773350601817, "features": "[\"alloc\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 2241668132362809309, "path": 10442351856655650194, "deps": [[1740877332521282793, "rand_core", false, 5278372777424255459], [3712811570531045576, "byteorder", false, 1857990998247885174], [6374421995994392543, "digest", false, 6373137742792284953], [6528079939221783635, "zeroize", false, 10736665073443638483], [17003143334332120809, "subtle", false, 5591828839113347267]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\curve25519-dalek-4936e6cc22b6537a\\dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}